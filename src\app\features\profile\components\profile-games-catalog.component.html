<!-- Profile Games Catalog -->
<div class="space-y-4 lg:space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-2xl lg:text-3xl font-black text-white mb-2">
        Каталог игр
      </h1>
      <p class="text-sm lg:text-base text-gray-300">
        Найдено {{ totalGames }} {{ totalGames === 1 ? 'игра' : totalGames < 5 ? 'игры' : 'игр' }}
      </p>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="bg-slate-800/40 border border-slate-600/50 rounded-lg p-4 lg:p-6">
    <div class="flex flex-col sm:flex-row gap-3 lg:gap-4">
      <!-- Search Input -->
      <div class="flex-1">
        <input
          type="text"
          [(ngModel)]="searchTerm"
          (input)="onSearchChange()"
          (keyup.enter)="onSearch()"
          placeholder="Поиск игр..."
          class="w-full px-3 lg:px-4 py-2 lg:py-2.5 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm lg:text-base"
        >
      </div>

      <!-- Sort Dropdown -->
      <div class="sm:w-40 lg:w-48">
        <select
          [(ngModel)]="sortBy"
          (change)="onSortChange()"
          class="w-full px-3 lg:px-4 py-2 lg:py-2.5 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm lg:text-base min-h-[44px] sm:min-h-auto"
        >
          <option value="title">По названию (А-Я)</option>
          <option value="-title">По названию (Я-А)</option>
          <option value="price">По цене (возр.)</option>
          <option value="-price">По цене (убыв.)</option>
          <option value="-created_at">Сначала новые</option>
          <option value="created_at">Сначала старые</option>
        </select>
      </div>

      <!-- Search Button -->
      <button
        (click)="onSearch()"
        class="px-4 lg:px-6 py-2 lg:py-2.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors font-medium text-sm lg:text-base min-h-[44px] sm:min-h-auto"
      >
        Поиск
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </div>

  <!-- Error State -->
  <div *ngIf="!loading && error" class="text-center py-12">
    <div class="text-red-400 mb-4">{{ error }}</div>
    <button
      (click)="loadGames()"
      class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
    >
      Попробовать снова
    </button>
  </div>

  <!-- Games Grid -->
  <div *ngIf="!loading && !error" class="space-y-6 lg:space-y-8">
    <!-- Games Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-6">
      <div
        *ngFor="let game of games"
        class="bg-slate-800/40 border border-slate-600/50 rounded-lg overflow-hidden hover:border-slate-500/60 transition-all duration-300 hover:transform hover:scale-[1.02] cursor-pointer group"
        (click)="viewGameDetails(game.id)"
      >
        <!-- Game Cover Image -->
        <div class="h-40 sm:h-44 lg:h-48 bg-gradient-to-br from-slate-700 to-slate-800 relative overflow-hidden">
          <img
            *ngIf="game.cover_image"
            [src]="game.cover_image"
            [alt]="game.title"
            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          >
          <div
            *ngIf="!game.cover_image"
            class="w-full h-full flex items-center justify-center"
          >
            <span class="text-gray-400 text-lg">Нет изображения</span>
          </div>
        </div>

        <!-- Game Info -->
        <div class="p-3 lg:p-4">
          <h3 class="font-bold text-base lg:text-lg text-white mb-2 line-clamp-1">{{ game.title }}</h3>
          <p class="text-gray-300 text-xs lg:text-sm mb-3 lg:mb-4 line-clamp-2">{{ game.description }}</p>

          <!-- Price and Actions -->
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
            <div class="text-blue-400 font-bold text-base lg:text-lg">
              {{ game.price }}₽
            </div>

            <!-- Action Button -->
            <button
              *ngIf="!canPlay(game)"
              (click)="needsAccessExtension(game) ? viewGameDetails(game.id) : (isInCart(game) ? $event.stopPropagation() : addToCart(game, $event))"
              [disabled]="isInCart(game) || isInLibrary(game)"
              [ngClass]="{
                'bg-gray-600 cursor-not-allowed': isInCart(game) || (isInLibrary(game) && !needsAccessExtension(game)),
                'bg-orange-600 hover:bg-orange-700': needsAccessExtension(game),
                'bg-blue-600 hover:bg-blue-700': !isInCart(game) && !isInLibrary(game) && !needsAccessExtension(game)
              }"
              class="px-3 lg:px-4 py-2 text-white text-xs lg:text-sm rounded-lg transition-colors font-medium min-h-[44px] sm:min-h-auto"
            >
              {{
                needsAccessExtension(game) ? 'Продлить доступ' :
                isInCart(game) ? 'В корзине' :
                isInLibrary(game) ? 'В библиотеке' : 'В корзину'
              }}
            </button>
            
            <!-- Play Button -->
            <button
              *ngIf="canPlay(game)"
              (click)="viewGameDetails(game.id)"
              class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white text-sm rounded-lg transition-colors font-medium"
            >
              Играть
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div *ngIf="totalPages > 1" class="flex justify-center items-center space-x-2 mt-8">
      <!-- Previous Button -->
      <button
        (click)="onPageChange(currentPage - 1)"
        [disabled]="currentPage === 1"
        class="px-3 py-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
      >
        ←
      </button>

      <!-- Page Numbers -->
      <button
        *ngFor="let page of getPages()"
        (click)="onPageChange(page)"
        [ngClass]="{
          'bg-blue-600 text-white': page === currentPage,
          'bg-slate-700 hover:bg-slate-600 text-gray-300': page !== currentPage
        }"
        class="px-3 py-2 rounded-lg transition-colors min-w-[40px]"
      >
        {{ page }}
      </button>

      <!-- Next Button -->
      <button
        (click)="onPageChange(currentPage + 1)"
        [disabled]="currentPage === totalPages"
        class="px-3 py-2 bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
      >
        →
      </button>
    </div>

    <!-- Empty State -->
    <div *ngIf="games.length === 0" class="text-center py-12">
      <div class="text-gray-400 mb-4">Игры не найдены</div>
      <button
        (click)="searchTerm = ''; onSearch()"
        class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
      >
        Сбросить фильтры
      </button>
    </div>
  </div>
</div>

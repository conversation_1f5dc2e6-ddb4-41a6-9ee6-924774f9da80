/* Admin Page Styles */
.admin-layout {
  min-height: calc(100vh - 5rem);
}

.admin-sidebar {
  /* Sidebar specific styles */
  scrollbar-width: thin;
  scrollbar-color: rgba(148, 163, 184, 0.3) transparent;
}

.admin-sidebar::-webkit-scrollbar {
  width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.admin-sidebar::-webkit-scrollbar-thumb {
  background-color: rgba(148, 163, 184, 0.3);
  border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(148, 163, 184, 0.5);
}

.admin-content {
  /* Content area styles */
  transition: margin-left 0.3s ease-in-out;
}

/* Mobile sidebar styles */
@media (max-width: 1023px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-sidebar.translate-x-0 {
    transform: translateX(0);
  }
  
  .admin-content {
    margin-left: 0 !important;
  }
}

/* Prevent body scroll when mobile sidebar is open */
:global(body.mobile-sidebar-open) {
  overflow: hidden;
}

/* Loading spinner overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

/* Responsive adjustments */
@media (min-width: 1024px) {
  .admin-content {
    margin-left: 20rem; /* 80 * 0.25rem = 20rem */
  }
}

/* Animation for route transitions */
.route-transition {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

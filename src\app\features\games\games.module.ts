import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { AccessTypeModalComponent } from '../../shared/components/access-type-modal/access-type-modal.component';
import { GamesCatalogComponent } from './games-catalog.component';
import { GameDetailComponent } from './game-detail.component';

@NgModule({
  declarations: [
    GamesCatalogComponent,
    GameDetailComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    SharedModule,
    AccessTypeModalComponent
  ]
})
export class GamesModule { }

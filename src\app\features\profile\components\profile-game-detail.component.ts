import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GameService } from '../../../core/services/game.service';
import { CartService } from '../../../core/services/cart.service';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { ActivationService } from '../../../core/services/activation.service';
import { Game } from '../../../core/models/game.model';
import { Cart } from '../../../core/models/cart.model';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-profile-game-detail',
  standalone: false,
  templateUrl: './profile-game-detail.component.html',
  styleUrls: ['./profile-game-detail.component.css']
})
export class ProfileGameDetailComponent implements OnInit, OnDestroy {
  gameId: number | null = null;

  game: Game | null = null;
  loading = false;
  error = '';

  // Gallery
  showGalleryModal = false;
  currentImageIndex = 0;
  galleryImages: string[] = [];
  private keydownListener?: (event: KeyboardEvent) => void;

  // Cart
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  private cartSubscription?: Subscription;
  private cartChangeSubscription?: Subscription;

  // Activation
  activationLoading = false;

  // Download
  downloadLoading: { [key: number]: boolean } = {};

  constructor(
    private gameService: GameService,
    private cartService: CartService,
    private authService: AuthService,
    private modalService: ModalService,
    private activationService: ActivationService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Get game ID from route parameters
    this.route.params.subscribe(params => {
      this.gameId = +params['id'];
      if (this.gameId) {
        this.loadGame();
      }
    });
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    if (this.cartSubscription) {
      this.cartSubscription.unsubscribe();
    }
    if (this.cartChangeSubscription) {
      this.cartChangeSubscription.unsubscribe();
    }
    this.removeKeydownListener();
  }

  private setupCartSubscription(): void {
    // Subscribe to cart data
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cart = cart;
    });

    // Subscribe to cart changes to update game states
    this.cartChangeSubscription = this.cartService.cartChanges$.subscribe(change => {
      // Update the current game's is_in_cart status if it matches
      if (this.game && this.game.id === change.gameId) {
        this.game.is_in_cart = change.action === 'added';
      }
    });
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.setupGallery();
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить информацию об игре';
        this.loading = false;
      }
    });
  }

  private setupGallery(): void {
    if (this.game?.gallery_items) {
      this.galleryImages = this.game.gallery_items.map(item => item.file);
    }
  }

  // Gallery methods
  openGallery(index: number = 0): void {
    if (this.galleryImages.length === 0) return;
    
    this.currentImageIndex = index;
    this.showGalleryModal = true;
    this.addKeydownListener();
  }

  closeGallery(): void {
    this.showGalleryModal = false;
    this.removeKeydownListener();
  }

  nextImage(): void {
    if (this.currentImageIndex < this.galleryImages.length - 1) {
      this.currentImageIndex++;
    }
  }

  prevImage(): void {
    if (this.currentImageIndex > 0) {
      this.currentImageIndex--;
    }
  }

  private addKeydownListener(): void {
    this.keydownListener = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        this.prevImage();
      } else if (event.key === 'ArrowRight') {
        this.nextImage();
      } else if (event.key === 'Escape') {
        this.closeGallery();
      }
    };
    document.addEventListener('keydown', this.keydownListener);
  }

  private removeKeydownListener(): void {
    if (this.keydownListener) {
      document.removeEventListener('keydown', this.keydownListener);
      this.keydownListener = undefined;
    }
  }

  // Game status methods
  isInCart(): boolean {
    return this.game?.is_in_cart || false;
  }

  isInLibrary(): boolean {
    return this.game?.is_in_library || false;
  }

  hasAccess(): boolean {
    return this.game?.has_access || false;
  }

  canPlay(): boolean {
    return this.isInLibrary() && this.hasAccess();
  }

  canBuy(): boolean {
    return !this.isInLibrary() && !this.isInCart();
  }

  needsAccessExtension(): boolean {
    return this.isInLibrary() && !this.hasAccess();
  }

  hasUnactivatedAccess(): boolean {
    return this.game?.has_unactivated_access || false;
  }

  canActivate(): boolean {
    return this.isInLibrary() && this.hasUnactivatedAccess() && !this.hasAccess();
  }

  // Cart actions
  addToCart(): void {
    if (!this.game) return;

    // Allow adding to cart if game is in library but access has expired (for access extension)
    if (this.isInLibrary() && this.hasAccess()) {
      this.modalService.error('Игра уже в библиотеке', 'У вас уже есть активный доступ к этой игре');
      return;
    }

    const isExtension = this.needsAccessExtension();
    const actionText = isExtension ? 'для продления доступа' : '';

    this.cartService.addToCart(this.game).subscribe({
      next: () => {
        if (this.game) {
          this.game.is_in_cart = true;
        }
        this.modalService.success(
          'Добавлено в корзину',
          `Игра "${this.game?.title}" добавлена в корзину ${actionText}.`
        );
        console.log('Game added to cart successfully');
      },
      error: (error) => {
        console.error('Error adding game to cart:', error.message);
        this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
      }
    });
  }

  // Activation actions
  activateGame(): void {
    if (!this.game || !this.canActivate()) return;

    this.modalService.confirm(
      'Активация доступа',
      `Вы уверены, что хотите активировать доступ к игре "${this.game.title}" на 1 день? Отсчёт времени начнётся сейчас.`,
      'Активировать',
      'Отмена'
    ).then(confirmed => {
      if (confirmed && this.game) {
        this.processActivation();
      }
    });
  }

  private processActivation(): void {
    if (!this.game) return;

    this.activationLoading = true;

    this.activationService.activateGameNow(this.game.id).subscribe({
      next: (response) => {
        this.activationLoading = false;

        // Update game data to reflect activation
        if (this.game) {
          this.game.has_access = true;
          this.game.has_unactivated_access = false;
          this.game.access_end = response.access_end;
        }

        // Show success message
        const endDate = new Date(response.access_end);
        const endDateText = endDate.toLocaleDateString('ru-RU', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });

        this.modalService.success(
          'Доступ активирован',
          `Доступ к игре "${this.game?.title}" успешно активирован! Доступ действует до ${endDateText}.`
        );
      },
      error: (error) => {
        this.activationLoading = false;
        console.error('Activation error:', error.message);
        this.modalService.error('Ошибка активации', error.message);
      }
    });
  }

  closeModal(): void {
    // Check if we came from library or games catalog
    const currentUrl = this.router.url;
    if (currentUrl.includes('/profile/library/games/')) {
      this.router.navigate(['/profile/library']);
    } else {
      // Navigate to main page games section instead of profile games catalog
      this.router.navigate(['/']).then(() => {
        setTimeout(() => {
          const element = document.getElementById('games');
          if (element) {
            const headerHeight = 80;
            const elementPosition = element.offsetTop - headerHeight;
            window.scrollTo({
              top: elementPosition,
              behavior: 'smooth'
            });
          }
        }, 100);
      });
    }
  }

  getBackButtonText(): string {
    const currentUrl = this.router.url;
    if (currentUrl.includes('/profile/library/games/')) {
      return 'Назад к библиотеке';
    } else {
      return 'Назад к играм';
    }
  }

  // Download methods
  downloadGameFile(gameFile: any): void {
    if (!gameFile || !gameFile.file) return;

    this.downloadLoading[gameFile.id] = true;

    // Create download URL using the file path
    const downloadUrl = `${environment.apiUrl}${gameFile.file}`;

    // Create a temporary anchor element to trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = gameFile.file_name;
    link.target = '_blank';

    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset loading state after a short delay
    setTimeout(() => {
      this.downloadLoading[gameFile.id] = false;
    }, 1000);
  }

  getActiveGameFiles(): any[] {
    return this.game?.game_files?.filter(file => file.is_active) || [];
  }

  getPlatformLabel(platform: string): string {
    const labels: { [key: string]: string } = {
      'windows': 'Windows',
      'mac': 'macOS',
      'linux': 'Linux',
      'android': 'Android',
      'ios': 'iOS',
      'web': 'Web'
    };
    return labels[platform] || platform;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Utility methods
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatPrice(price: string): string {
    const num = parseFloat(price);
    return num.toLocaleString('ru-RU', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  }
}

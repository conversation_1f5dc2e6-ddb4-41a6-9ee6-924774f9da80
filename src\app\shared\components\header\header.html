<!-- Header Section -->
<header
  [class]="isProfilePage
    ? 'fixed top-0 left-0 right-0 z-[60] bg-slate-900/95 backdrop-blur-sm border-b border-slate-600/30'
    : 'fixed top-0 left-0 right-0 z-[60] bg-black/20 backdrop-blur-md border-b border-white/10'"
>
  <!-- Always use full width container -->
  <div class="w-full px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/">
          <img
            src="/assets/icons/logo.svg"
            class="h-12 w-auto"
            alt="TOY FOR TOI"
          />
        </a>
      </div>

      <!-- Navigation -->
      <div class="flex items-center gap-6">
        <nav class="hidden md:flex space-x-8">
          <a
            (click)="navigateToGames($event)"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            Игры
          </a>
          <a
            (click)="scrollToSection('about'); $event.preventDefault()"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            О нас
          </a>
          <a
            (click)="scrollToSection('pricing'); $event.preventDefault()"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            Тарифы
          </a>
          <a
            (click)="navigateToCart($event)"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors flex items-center gap-2 text-sm font-medium relative cursor-pointer"
          >
            Корзина
            <img src="/assets/icons/cart.svg" class="w-4 h-4" alt="" />
            <span *ngIf="cartItemCount > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {{ cartItemCount }}
            </span>
          </a>
        </nav>

        <!-- User Menu / Login Button -->
        <div *ngIf="!isAuthenticated">
          <a
            href="/login"
            class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 text-sm font-medium shadow-lg"
          >
            Личный кабинет
          </a>
        </div>

        <!-- User Menu when authenticated - Desktop Only -->
        <div *ngIf="isAuthenticated" class="relative hidden md:block">
          <!-- Dropdown Button -->
          <button
            (click)="toggleUserDropdown()"
            class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 text-sm font-medium shadow-lg flex items-center gap-2"
          >
            {{ currentUser?.email }}
            <svg
              class="w-4 h-4 transition-transform duration-200"
              [class.rotate-180]="isUserDropdownOpen"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            *ngIf="isUserDropdownOpen"
            class="absolute right-0 mt-2 w-48 bg-slate-800/95 backdrop-blur-sm border border-slate-600/50 rounded-lg shadow-xl z-[70] overflow-hidden"
          >
            <!-- Personal Account -->
            <a
              href="/profile"
              (click)="closeUserDropdown()"
              class="block px-4 py-3 text-white hover:bg-slate-700/50 transition-colors border-b border-slate-600/30"
            >
              <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-sm font-medium">Личный кабинет</span>
              </div>
            </a>

            <!-- Admin Panel (Only for Staff) -->
            <a
              *ngIf="currentUser?.is_staff"
              href="/admin"
              (click)="closeUserDropdown()"
              class="block px-4 py-3 text-white hover:bg-slate-700/50 transition-colors border-b border-slate-600/30"
            >
              <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm font-medium">Админ панель</span>
              </div>
            </a>

            <!-- Logout -->
            <button
              (click)="onLogout(); closeUserDropdown()"
              class="w-full text-left px-4 py-3 text-white hover:bg-red-600/20 transition-colors"
            >
              <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                <span class="text-sm font-medium">Выйти</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button
          class="md:hidden text-white p-2 hover:bg-white/10 rounded-lg transition-all duration-200"
          (click)="toggleMenu()"
        >
          <div class="w-6 h-6 flex flex-col justify-center items-center">
            <span
              class="block w-5 h-0.5 bg-current transition-all duration-300 ease-in-out"
              [class.rotate-45]="isMenuOpen"
              [class.translate-y-1.5]="isMenuOpen"
            ></span>
            <span
              class="block w-5 h-0.5 bg-current mt-1 transition-all duration-300 ease-in-out"
              [class.opacity-0]="isMenuOpen"
            ></span>
            <span
              class="block w-5 h-0.5 bg-current mt-1 transition-all duration-300 ease-in-out"
              [class.-rotate-45]="isMenuOpen"
              [class.-translate-y-1.5]="isMenuOpen"
            ></span>
          </div>
        </button>
      </div>
    </div>

    <!-- Mobile Menu Overlay -->
    <div
      *ngIf="isMenuOpen"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm z-[50] md:hidden"
      (click)="toggleMenu()"
    ></div>

    <!-- Mobile Menu -->
    <div
      class="md:hidden fixed top-[80px] left-0 right-0 z-[60] transition-all duration-300 ease-in-out"
      [class.opacity-0]="!isMenuOpen"
      [class.opacity-100]="isMenuOpen"
      [class.translate-y-[-10px]]="!isMenuOpen"
      [class.translate-y-0]="isMenuOpen"
      [class.pointer-events-none]="!isMenuOpen"
    >
      <div class="mx-4 bg-slate-800/95 backdrop-blur-md rounded-lg shadow-2xl border border-slate-600/30 overflow-hidden">
        <!-- Navigation Links -->
        <div class="py-2">
          <!-- Games -->
          <a
            (click)="navigateToGames($event); toggleMenu()"
            class="block px-4 py-3 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200 cursor-pointer text-sm"
          >
            Игры
          </a>

          <!-- About Us -->
          <a
            (click)="scrollToSection('about'); toggleMenu(); $event.preventDefault()"
            class="block px-4 py-3 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200 cursor-pointer text-sm"
          >
            О нас
          </a>

          <!-- Tariffs -->
          <a
            (click)="scrollToSection('pricing'); toggleMenu(); $event.preventDefault()"
            class="block px-4 py-3 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200 cursor-pointer text-sm"
          >
            Тарифы
          </a>

          <!-- Cart -->
          <a
            (click)="navigateToCart($event); toggleMenu()"
            class="flex items-center justify-between px-4 py-3 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200 cursor-pointer text-sm"
          >
            <span>Корзина</span>
            <span *ngIf="cartItemCount > 0" class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {{ cartItemCount }}
            </span>
          </a>
        </div>

        <!-- Separator -->
        <div class="border-t border-slate-600/30"></div>

        <!-- User Account Section -->
        <div class="py-2">
          <!-- Personal Account (when not authenticated) -->
          <a
            *ngIf="!isAuthenticated"
            href="/login"
            (click)="toggleMenu()"
            class="block mx-3 my-2 px-4 py-3 text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-medium transition-all duration-200 hover:from-blue-700 hover:to-purple-700 text-sm text-center"
          >
            Личный кабинет
          </a>

          <!-- When authenticated -->
          <div *ngIf="isAuthenticated">
            <!-- Admin Panel (Only for Staff) -->
            <a
              *ngIf="currentUser?.is_staff"
              href="/admin"
              (click)="toggleMenu()"
              class="block px-4 py-3 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200 text-sm"
            >
              Админ панель
            </a>

            <!-- Personal Account (highlighted button) -->
            <a
              href="/profile"
              (click)="toggleMenu()"
              class="block mx-3 my-2 px-4 py-3 text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg font-medium transition-all duration-200 hover:from-blue-700 hover:to-purple-700 text-sm text-center"
            >
              Личный кабинет
            </a>

            <!-- Logout -->
            <button
              (click)="onLogout(); toggleMenu()"
              class="w-full text-left px-4 py-3 text-gray-300 hover:text-red-400 hover:bg-red-600/10 transition-all duration-200 text-sm"
            >
              Выйти
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>

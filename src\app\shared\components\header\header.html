<!-- Header Section -->
<header
  [class]="isProfilePage
    ? 'fixed top-0 left-0 right-0 z-[60] bg-slate-900/95 backdrop-blur-sm border-b border-slate-600/30'
    : 'fixed top-0 left-0 right-0 z-[60] bg-black/20 backdrop-blur-md border-b border-white/10'"
>
  <!-- Always use full width container -->
  <div class="w-full px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Logo -->
      <div class="flex items-center">
        <a href="/">
          <img
            src="/assets/icons/logo.svg"
            class="h-12 w-auto"
            alt="TOY FOR TOI"
          />
        </a>
      </div>

      <!-- Navigation -->
      <div class="flex items-center gap-6">
        <nav class="hidden md:flex space-x-8">
          <a
            (click)="navigateToGames($event)"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            Игры
          </a>
          <a
            (click)="scrollToSection('about'); $event.preventDefault()"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            О нас
          </a>
          <a
            (click)="scrollToSection('pricing'); $event.preventDefault()"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors text-sm font-medium cursor-pointer"
          >
            Тарифы
          </a>
          <a
            (click)="navigateToCart($event)"
            class="text-[#CFF5FF] hover:text-purple-300 transition-colors flex items-center gap-2 text-sm font-medium relative cursor-pointer"
          >
            Корзина
            <img src="/assets/icons/cart.svg" class="w-4 h-4" alt="" />
            <span *ngIf="cartItemCount > 0" class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
              {{ cartItemCount }}
            </span>
          </a>
        </nav>

        <!-- User Menu / Login Button -->
        <div *ngIf="!isAuthenticated">
          <a
            href="/login"
            class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-2 rounded-full hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105 text-sm font-medium shadow-lg"
          >
            Личный кабинет
          </a>
        </div>

        <!-- User Menu when authenticated - Desktop Only -->
        <div *ngIf="isAuthenticated" class="relative hidden md:block">
          <!-- Dropdown Button -->
          <button
            (click)="toggleUserDropdown()"
            class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full hover:from-blue-700 hover:to-purple-700 transition-all transform hover:scale-105 text-sm font-medium shadow-lg flex items-center gap-2"
          >
            {{ currentUser?.email }}
            <svg
              class="w-4 h-4 transition-transform duration-200"
              [class.rotate-180]="isUserDropdownOpen"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>

          <!-- Dropdown Menu -->
          <div
            *ngIf="isUserDropdownOpen"
            class="absolute right-0 mt-2 w-48 bg-slate-800/95 backdrop-blur-sm border border-slate-600/50 rounded-lg shadow-xl z-[70] overflow-hidden"
          >
            <!-- Personal Account -->
            <a
              href="/profile"
              (click)="closeUserDropdown()"
              class="block px-4 py-3 text-white hover:bg-slate-700/50 transition-colors border-b border-slate-600/30"
            >
              <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <span class="text-sm font-medium">Личный кабинет</span>
              </div>
            </a>

            <!-- Admin Panel (Only for Staff) -->
            <a
              *ngIf="currentUser?.is_staff"
              href="/admin"
              (click)="closeUserDropdown()"
              class="block px-4 py-3 text-white hover:bg-slate-700/50 transition-colors border-b border-slate-600/30"
            >
              <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm font-medium">Админ панель</span>
              </div>
            </a>

            <!-- Logout -->
            <button
              (click)="onLogout(); closeUserDropdown()"
              class="w-full text-left px-4 py-3 text-white hover:bg-red-600/20 transition-colors"
            >
              <div class="flex items-center gap-3">
                <svg class="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                <span class="text-sm font-medium">Выйти</span>
              </div>
            </button>
          </div>
        </div>

        <!-- Mobile Menu Button -->
        <button class="md:hidden text-white p-2" (click)="toggleMenu()">
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"
            ></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div class="md:hidden" [class.hidden]="!isMenuOpen">
      <div
        class="px-2 pt-2 pb-3 space-y-1 bg-black/40 backdrop-blur-md rounded-lg mt-2"
      >
        <!-- Admin Link - Mobile Only (Only for Staff) -->
        <a
          *ngIf="isAuthenticated && currentUser?.is_staff"
          href="/admin"
          (click)="toggleMenu()"
          class="block px-3 py-2 text-white bg-gradient-to-r from-red-600 to-orange-600 rounded-lg mb-2 font-medium transition-colors"
        >
          Админ панель
        </a>

        <!-- Profile Link - Mobile Only -->
        <a
          *ngIf="isAuthenticated"
          href="/profile"
          (click)="toggleMenu()"
          class="block px-3 py-2 text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg mb-2 font-medium transition-colors"
        >
          Личный кабинет
        </a>

        <!-- Logout Button - Mobile Only -->
        <button
          *ngIf="isAuthenticated"
          (click)="onLogout(); toggleMenu()"
          class="w-full text-left block px-3 py-2 text-white bg-gradient-to-r from-red-600 to-red-700 rounded-lg mb-2 font-medium transition-colors"
        >
          Выйти
        </button>

        <!-- Login Link - Mobile Only (when not authenticated) -->
        <a
          *ngIf="!isAuthenticated"
          href="/login"
          (click)="toggleMenu()"
          class="block px-3 py-2 text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg mb-2 font-medium transition-colors"
        >
          Личный кабинет
        </a>

        <a
          (click)="navigateToGames($event); toggleMenu()"
          class="block px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors cursor-pointer"
          >Игры</a
        >
        <a
          (click)="scrollToSection('about'); toggleMenu(); $event.preventDefault()"
          class="block px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors cursor-pointer"
          >О нас</a
        >
        <a
          (click)="scrollToSection('pricing'); toggleMenu(); $event.preventDefault()"
          class="block px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors cursor-pointer"
          >Тарифы</a
        >
        <a
          (click)="navigateToCart($event)"
          class="flex px-3 py-2 text-[#CFF5FF] hover:text-purple-300 transition-colors items-center justify-between cursor-pointer"
        >
          Корзина
          <span *ngIf="cartItemCount > 0" class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {{ cartItemCount }}
          </span>
        </a>
      </div>
    </div>
  </div>
</header>

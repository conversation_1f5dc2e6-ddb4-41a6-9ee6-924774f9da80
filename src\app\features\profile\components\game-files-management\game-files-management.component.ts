import { Component, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GameFileService } from '../../../../core/services/game-file.service';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import {
  GameFile,
  CreateGameFileRequest,
  UpdateGameFileRequest,
  GameFileFilters,
  GameFileError
} from '../../../../core/models/game.model';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-files-management',
  standalone: false,
  templateUrl: './game-files-management.component.html',
  styleUrl: './game-files-management.component.css'
})
export class GameFilesManagementComponent implements OnInit {
  gameFiles: GameFile[] = [];
  games: Game[] = [];
  gameFilesLoading = false;
  gameFilesError = '';

  // Pagination
  totalGameFiles = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-id';
  selectedGame: number | null = null;
  selectedPlatform: string = 'all';
  selectedActiveStatus: 'all' | 'active' | 'inactive' = 'all';
  private searchSubject = new Subject<string>();

  // Platform options
  platformOptions = [
    { value: 'all', label: 'Все платформы' },
    { value: 'windows', label: 'Windows' },
    { value: 'mac', label: 'macOS' },
    { value: 'linux', label: 'Linux' },
    { value: 'android', label: 'Android' },
    { value: 'ios', label: 'iOS' },
    { value: 'web', label: 'Web' }
  ];

  // Add game file form
  showAddForm = false;
  addGameFileLoading = false;
  addGameFileError = '';
  selectedFile: File | null = null;
  newGameFile: CreateGameFileRequest = {
    game: 0,
    file: null as any,
    platform: 'windows'
  };

  // Edit game file modal
  showEditModal = false;
  editGameFileLoading = false;
  editGameFileError = '';
  editingGameFile: GameFile | null = null;
  editGameFileData: UpdateGameFileRequest = {
    platform: 'windows',
    version: '',
    description: '',
    is_active: true
  };

  constructor(
    private gameFileService: GameFileService,
    private gameService: GameService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadGameFiles();
    this.loadGames();
    this.setupSearch();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadGameFiles();
    });
  }

  loadGameFiles(): void {
    this.gameFilesLoading = true;
    this.gameFilesError = '';

    const filters = this.buildFilters();

    this.gameFileService.getGameFiles(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.gameFiles = response.results;
        this.totalGameFiles = response.count;
        this.hasNext = !!response.next;
        this.hasPrevious = !!response.previous;
        this.gameFilesLoading = false;
      },
      error: (error) => {
        this.gameFilesError = error.message || 'Failed to load game files';
        this.gameFilesLoading = false;
      }
    });
  }

  loadGames(): void {
    this.gameService.getGames().subscribe({
      next: (response) => {
        this.games = response.results;
      },
      error: (error) => {
        console.error('Failed to load games:', error);
      }
    });
  }

  private buildFilters(): GameFileFilters {
    const filters: GameFileFilters = {
      ordering: this.sortBy
    };

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.selectedGame) {
      filters.game = this.selectedGame;
    }

    if (this.selectedPlatform !== 'all') {
      filters.platform = this.selectedPlatform;
    }

    if (this.selectedActiveStatus !== 'all') {
      filters.is_active = this.selectedActiveStatus === 'active';
    }

    return filters;
  }

  // Search and filter methods
  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onGameFilterChange(): void {
    this.currentPage = 1;
    this.loadGameFiles();
  }

  onPlatformFilterChange(): void {
    this.currentPage = 1;
    this.loadGameFiles();
  }

  onActiveStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadGameFiles();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadGameFiles();
  }

  // Pagination methods
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadGameFiles();
  }

  get totalPages(): number {
    return Math.ceil(this.totalGameFiles / this.pageSize);
  }

  get pages(): number[] {
    const pages = [];
    const maxPages = Math.min(this.totalPages, 10);
    const startPage = Math.max(1, this.currentPage - 5);
    const endPage = Math.min(this.totalPages, startPage + maxPages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // File selection and validation
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file size (max 500MB)
      if (file.size > 500 * 1024 * 1024) {
        this.addGameFileError = 'Размер файла не должен превышать 500MB';
        return;
      }

      this.selectedFile = file;
      this.newGameFile.file = file;
      this.addGameFileError = '';
    }
  }

  // Add game file methods
  toggleAddForm(): void {
    this.showAddForm = !this.showAddForm;
    if (this.showAddForm) {
      this.resetAddForm();
    }
  }

  private resetAddForm(): void {
    this.newGameFile = {
      game: 0,
      file: null as any,
      platform: 'windows'
    };
    this.selectedFile = null;
    this.addGameFileError = '';
  }

  onAddGameFile(): void {
    if (!this.newGameFile.game || !this.selectedFile) {
      this.addGameFileError = 'Пожалуйста, выберите игру и файл';
      return;
    }

    this.addGameFileLoading = true;
    this.addGameFileError = '';

    this.gameFileService.createGameFile(this.newGameFile).subscribe({
      next: (gameFile) => {
        this.gameFiles.unshift(gameFile);
        this.totalGameFiles++;
        this.showAddForm = false;
        this.addGameFileLoading = false;
        this.modalService.success('Успех', 'Файл игры успешно добавлен');
      },
      error: (error) => {
        this.addGameFileLoading = false;
        if (error && typeof error === 'object') {
          const gameFileError = error as GameFileError;
          if (gameFileError.game) {
            this.addGameFileError = 'Игра: ' + gameFileError.game.join(', ');
          } else if (gameFileError.file) {
            this.addGameFileError = 'Файл: ' + gameFileError.file.join(', ');
          } else if (gameFileError.platform) {
            this.addGameFileError = 'Платформа: ' + gameFileError.platform.join(', ');
          } else if (gameFileError.non_field_errors) {
            this.addGameFileError = gameFileError.non_field_errors.join(', ');
          } else {
            this.addGameFileError = 'Произошла ошибка при добавлении файла';
          }
        } else {
          this.addGameFileError = error.message || 'Произошла ошибка при добавлении файла';
        }
      }
    });
  }

  // Download game file
  downloadGameFile(gameFile: GameFile): void {
    this.gameFileService.triggerFileDownload(gameFile.id, gameFile.file_name).subscribe({
      next: () => {
        // Download started successfully
      },
      error: (error) => {
        this.modalService.error('Ошибка', 'Не удалось скачать файл: ' + error.message);
      }
    });
  }

  // Edit game file methods
  openEditModal(gameFile: GameFile): void {
    this.editingGameFile = gameFile;
    this.editGameFileData = {
      platform: gameFile.platform,
      version: gameFile.version,
      description: gameFile.description || '',
      is_active: gameFile.is_active
    };
    this.editGameFileError = '';
    this.showEditModal = true;
  }

  closeEditModal(): void {
    this.showEditModal = false;
    this.editingGameFile = null;
    this.editGameFileError = '';
  }

  onUpdateGameFile(): void {
    if (!this.editingGameFile) return;

    this.editGameFileLoading = true;
    this.editGameFileError = '';

    this.gameFileService.updateGameFile(this.editingGameFile.id, this.editGameFileData).subscribe({
      next: (updatedGameFile) => {
        // Update the game file in the list
        const index = this.gameFiles.findIndex(gf => gf.id === updatedGameFile.id);
        if (index !== -1) {
          this.gameFiles[index] = updatedGameFile;
        }
        this.editGameFileLoading = false;
        this.closeEditModal();
        this.modalService.success('Успех', 'Файл игры успешно обновлен');
      },
      error: (error) => {
        this.editGameFileLoading = false;
        if (error && typeof error === 'object') {
          const gameFileError = error as GameFileError;
          if (gameFileError.platform) {
            this.editGameFileError = 'Платформа: ' + gameFileError.platform.join(', ');
          } else if (gameFileError.version) {
            this.editGameFileError = 'Версия: ' + gameFileError.version.join(', ');
          } else if (gameFileError.description) {
            this.editGameFileError = 'Описание: ' + gameFileError.description.join(', ');
          } else if (gameFileError.non_field_errors) {
            this.editGameFileError = gameFileError.non_field_errors.join(', ');
          } else {
            this.editGameFileError = 'Произошла ошибка при обновлении файла';
          }
        } else {
          this.editGameFileError = error.message || 'Произошла ошибка при обновлении файла';
        }
      }
    });
  }

  // Delete game file method
  deleteGameFile(gameFile: GameFile): void {
    this.modalService.confirm(
      'Удаление файла игры',
      `Вы уверены, что хотите удалить файл "${gameFile.file_name}"?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.gameFileService.deleteGameFile(gameFile.id).subscribe({
          next: () => {
            this.gameFiles = this.gameFiles.filter(gf => gf.id !== gameFile.id);
            this.totalGameFiles--;
            this.modalService.success('Успех', 'Файл игры успешно удален');
          },
          error: (error) => {
            this.modalService.error('Ошибка', 'Не удалось удалить файл игры: ' + error.message);
          }
        });
      }
    });
  }

  // Utility methods
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getGameTitle(gameId: number): string {
    const game = this.games.find(g => g.id === gameId);
    return game ? game.title : `Игра #${gameId}`;
  }

  getPlatformLabel(platform: string): string {
    const option = this.platformOptions.find(p => p.value === platform);
    return option ? option.label : platform;
  }

  getActiveStatusText(isActive: boolean): string {
    return isActive ? 'Активен' : 'Неактивен';
  }

  getActiveStatusClass(isActive: boolean): string {
    return isActive ? 'text-green-400' : 'text-red-400';
  }
}

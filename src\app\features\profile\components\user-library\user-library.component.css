/* User Library Component Styles */

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for search input */
input::-webkit-scrollbar {
  width: 4px;
}

input::-webkit-scrollbar-track {
  background: rgba(71, 85, 105, 0.3);
  border-radius: 2px;
}

input::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 2px;
}

input::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Hover effects for game cards */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 0.75rem;
  }

  /* Compact search and filter controls */
  .bg-slate-800\/40 {
    padding: 0.75rem;
  }

  /* Smaller text on mobile */
  h2 {
    font-size: 1.25rem;
  }

  /* Touch-friendly buttons */
  button, select, input {
    min-height: 44px;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 0.875rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1rem;
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.25rem;
  }
}

@media (min-width: 1281px) {
  .grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1.5rem;
  }
}

/* Mobile-specific improvements */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .group:hover .group-hover\:scale-110 {
    transform: none;
  }

  .hover\:border-slate-500\/60:hover {
    border-color: inherit;
  }

  .hover\:transform:hover {
    transform: none;
  }

  .hover\:scale-\[1\.02\]:hover {
    transform: none;
  }
}

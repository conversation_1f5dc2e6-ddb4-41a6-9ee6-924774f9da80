import { User } from '../core/models/user.model';

export const SAMPLE_USERS: User[] = [
  {
    id: 1,
    email: "<EMAIL>",
    phone: null,
    is_active: true,
    is_staff: true,
    is_superuser: true,
    user_code: "ADM01",
    date_joined: "2024-01-01T10:00:00Z",
    last_login: "2025-06-27T08:30:00Z"
  },
  {
    id: 2,
    email: "<EMAIL>",
    phone: "8701234567",
    is_active: true,
    is_staff: false,
    is_superuser: false,
    user_code: "USR01",
    date_joined: "2024-02-15T12:30:00Z",
    last_login: null
  },
  {
    id: 3,
    email: "<EMAIL>",
    phone: null,
    is_active: true,
    is_staff: true,
    is_superuser: false,
    user_code: "MOD01",
    date_joined: "2024-03-10T09:15:00Z",
    last_login: "2025-06-26T14:20:00Z"
  },
  {
    id: 4,
    email: "<EMAIL>",
    phone: "8709876543",
    is_active: false,
    is_staff: false,
    is_superuser: false,
    user_code: "INA01",
    date_joined: "2024-04-05T16:45:00Z",
    last_login: "2024-05-01T10:30:00Z"
  },
  {
    id: 5,
    email: "<EMAIL>",
    phone: null,
    is_active: true,
    is_staff: false,
    is_superuser: false,
    user_code: "JOH01",
    date_joined: "2024-05-20T11:00:00Z",
    last_login: "2025-06-25T16:45:00Z"
  },
  {
    id: 6,
    email: "<EMAIL>",
    phone: "8705555555",
    is_active: true,
    is_staff: false,
    is_superuser: false,
    user_code: "JAN01",
    date_joined: "2024-06-01T13:30:00Z",
    last_login: "2025-06-27T09:15:00Z"
  },
  {
    id: 7,
    email: "<EMAIL>",
    phone: "8701111111",
    is_active: true,
    is_staff: true,
    is_superuser: false,
    user_code: "SUP01",
    date_joined: "2024-01-15T08:00:00Z",
    last_login: "2025-06-26T17:30:00Z"
  },
  {
    id: 8,
    email: "<EMAIL>",
    phone: null,
    is_active: false,
    is_staff: false,
    is_superuser: false,
    user_code: "TST01",
    date_joined: "2024-07-01T14:20:00Z",
    last_login: null
  }
];

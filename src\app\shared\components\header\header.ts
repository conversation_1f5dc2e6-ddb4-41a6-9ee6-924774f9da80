import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { AuthService } from '../../../core/services/auth.service';
import { CartService } from '../../../core/services/cart.service';
import { GuestCartService } from '../../../core/services/guest-cart.service';
import { CartRoutingService } from '../../../core/services/cart-routing.service';

@Component({
  selector: 'app-header',
  standalone: false,
  templateUrl: './header.html',
  styleUrl: './header.css'
})
export class Header implements OnInit, OnDestroy {
  isMenuOpen = false;
  isUserDropdownOpen = false;
  isAuthenticated = false;
  currentUser: any = null;
  cartItemCount = 0;
  isProfilePage = false;
  private authSubscription?: Subscription;
  private userSubscription?: Subscription;
  private cartSubscription?: Subscription;
  private guestCartSubscription?: Subscription;
  private routerSubscription?: Subscription;

  constructor(
    private authService: AuthService,
    private cartService: CartService,
    private guestCartService: GuestCartService,
    private cartRoutingService: CartRoutingService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to authentication status
    this.authSubscription = this.authService.isAuthenticated$.subscribe(
      isAuth => {
        this.isAuthenticated = isAuth;
        this.updateCartSubscription();
      }
    );

    // Subscribe to current user data
    this.userSubscription = this.authService.currentUser$.subscribe(
      user => this.currentUser = user
    );

    // Subscribe to router events to detect profile/admin page
    this.routerSubscription = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.isProfilePage = event.url.startsWith('/profile') || event.url.startsWith('/admin');
    });

    // Check initial route
    this.isProfilePage = this.router.url.startsWith('/profile') || this.router.url.startsWith('/admin');

    // Initialize cart subscription
    this.updateCartSubscription();
  }

  private updateCartSubscription(): void {
    // Unsubscribe from previous cart subscriptions
    this.cartSubscription?.unsubscribe();
    this.guestCartSubscription?.unsubscribe();

    if (this.isAuthenticated) {
      // Subscribe to authenticated user cart
      this.cartSubscription = this.cartService.cart$.subscribe(
        cart => this.cartItemCount = cart.total_items
      );
    } else {
      // Subscribe to guest cart
      this.guestCartSubscription = this.guestCartService.cart$.subscribe(
        cart => this.cartItemCount = cart.totalItems
      );
    }
  }

  ngOnDestroy(): void {
    this.authSubscription?.unsubscribe();
    this.userSubscription?.unsubscribe();
    this.cartSubscription?.unsubscribe();
    this.guestCartSubscription?.unsubscribe();
    this.routerSubscription?.unsubscribe();
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  toggleUserDropdown() {
    this.isUserDropdownOpen = !this.isUserDropdownOpen;
  }

  closeUserDropdown() {
    this.isUserDropdownOpen = false;
  }

  onLogout(): void {
    this.closeUserDropdown();
    this.authService.logout();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.relative');

    // Close dropdown if clicking outside of it
    if (!dropdown && this.isUserDropdownOpen) {
      this.closeUserDropdown();
    }
  }

  navigateToGames(event: Event): void {
    event.preventDefault();
    // Always navigate to main page games section (anchor) regardless of authentication
    this.scrollToSection('games');
  }

  /**
   * Smooth scroll to section with proper offset for fixed header
   */
  scrollToSection(sectionId: string): void {
    // If not on main page, navigate first
    if (this.router.url !== '/') {
      this.router.navigate(['/']).then(() => {
        setTimeout(() => {
          this.performSmoothScroll(sectionId);
        }, 100);
      });
    } else {
      this.performSmoothScroll(sectionId);
    }
  }

  /**
   * Perform the actual smooth scroll with header offset
   */
  private performSmoothScroll(sectionId: string): void {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80; // Approximate header height
      const elementPosition = element.offsetTop - headerHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
      });
    }
  }

  navigateToCart(event: Event): void {
    event.preventDefault();
    this.cartRoutingService.navigateToCart();
  }
}

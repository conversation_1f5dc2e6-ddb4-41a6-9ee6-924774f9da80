// API Response models
export interface ApiCartItem {
  id: number;
  user: number;
  game?: number;
  game_obj?: {
    id: number;
    title: string;
    game_code: string;
    subtitle?: string;
    description: string;
    how_to_play?: string;
    target_audience?: string;
    requires_device: boolean;
    price: string;
    trial_available: boolean;
    cover_image: string | null;
    system_requirements?: string;
    required_equipment?: string;
    created_at: string;
    is_in_cart?: boolean;
    is_in_library?: boolean;
    has_access?: boolean;
    access_end?: string | null;
    has_unactivated_access?: boolean;
    gallery_items?: any[];
  };
  game_package?: number;
  game_package_obj?: {
    id: number;
    name: string;
    description: string;
    price: string;
    benefit_1?: string;
    benefit_2?: string;
    benefit_3?: string;
    duration_days: number;
    games: {
      id: number;
      title: string;
    }[];
    max_selectable_games: number;
  };
  quantity: number;
  added_at: string;
}

export interface ApiCartResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: ApiCartItem[];
}

// Frontend models (using embedded API data)
export interface CartItem {
  id: number;
  user: number;
  game?: number;
  game_obj?: {
    id: number;
    title: string;
    game_code: string;
    subtitle?: string;
    description: string;
    how_to_play?: string;
    target_audience?: string;
    requires_device: boolean;
    price: string;
    trial_available: boolean;
    cover_image: string | null;
    system_requirements?: string;
    required_equipment?: string;
    created_at: string;
    is_in_cart?: boolean;
    is_in_library?: boolean;
    has_access?: boolean;
    access_end?: string | null;
    has_unactivated_access?: boolean;
    gallery_items?: any[];
  };
  game_package?: number;
  game_package_obj?: {
    id: number;
    name: string;
    description: string;
    price: string;
    benefit_1?: string;
    benefit_2?: string;
    benefit_3?: string;
    duration_days: number;
    games: {
      id: number;
      title: string;
    }[];
    max_selectable_games: number;
  };
  quantity: number;
  added_at: string;
}

export interface Cart {
  items: CartItem[];
  total_items: number;
  total_price: number;
}

// API Request models
export interface AddToCartRequest {
  game?: number;
  game_package?: number;
  quantity: number;
}

export interface AddMultipleToCartRequest {
  games?: number[];
  games_csv?: string;
  game_packages?: number[];
  game_packages_csv?: string;
}

export interface CartError {
  non_field_errors?: string[];
  game?: string[];
}

// Purchase models for checkout
export interface Purchase {
  id: number;
  game_title?: string;
  package_title?: string;
  purchase_type: 'game' | 'package';
  price: string;
  status: string;
  created_at: string;
}

export interface CheckoutResponse {
  purchases: Purchase[];
}

export interface CheckoutError {
  non_field_errors?: string[];
  detail?: string;
}

// Purchase API models
export interface PurchaseResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Purchase[];
}

export interface PaymentRequest {
  access_type: 'oneday' | 'subscription';
}

export interface PaymentResponse {
  detail: string;
}

export interface PaymentError {
  detail?: string;
  non_field_errors?: string[];
}

export interface PurchaseFilters {
  status?: 'paid' | 'pending' | 'failed';
  ordering?: string;
  search?: string;
}

// Access type selection interfaces
export interface AccessType {
  id: 'oneday' | 'subscription';
  name: string;
  description: string;
  duration: string;
  icon: string;
}

export interface AccessTypeSelection {
  purchaseId: number;
  gameTitle?: string;
  packageTitle?: string;
  price: string;
  accessType?: 'oneday' | 'subscription';
}

/* Access Type Modal Styles */
.access-type-option {
  transition: all 0.3s ease;
}

.access-type-option:hover {
  transform: translateY(-2px);
}

.access-type-option.selected {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Animation for modal entrance */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

/* Smooth transitions for selection indicators */
.selection-indicator {
  transition: all 0.2s ease;
}

/* Hover effects for better UX */
.access-type-card:hover .icon-container {
  transform: scale(1.1);
}

.access-type-card .icon-container {
  transition: transform 0.2s ease;
}

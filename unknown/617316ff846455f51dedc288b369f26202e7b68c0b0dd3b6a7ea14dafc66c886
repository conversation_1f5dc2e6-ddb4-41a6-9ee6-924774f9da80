<!-- Main Container with Unified Gradient Background -->
<div class="min-h-screen relative overflow-hidden">
  <!-- Static gradient background for entire page -->
  <div
    class="fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900/80 to-black"
  ></div>
  <div
    class="fixed inset-0 bg-gradient-to-b from-blue-900/10 via-purple-950/30 to-black/50"
  ></div>



  <!-- Floating decorative elements -->
  <div class="fixed inset-0 pointer-events-none z-5">
    <!-- Bottom left floating element -->
    <div
      class="absolute bottom-20 left-20 w-4 h-4 bg-white/20 rounded-full animate-float"
    ></div>
    <div
      class="absolute bottom-32 left-32 w-2 h-2 bg-blue-400/30 rounded-full animate-float"
      style="animation-delay: 2s"
    ></div>

    <!-- Top left floating elements -->
    <div
      class="absolute top-40 left-16 w-3 h-3 bg-purple-400/25 rounded-full animate-float"
      style="animation-delay: 1s"
    ></div>
    <div
      class="absolute top-60 left-40 w-2 h-2 bg-white/15 rounded-full animate-float"
      style="animation-delay: 3s"
    ></div>

    <!-- Right side floating elements -->
    <div
      class="absolute top-1/3 right-20 w-3 h-3 bg-blue-300/20 rounded-full animate-float"
      style="animation-delay: 4s"
    ></div>
    <div
      class="absolute bottom-1/3 right-32 w-2 h-2 bg-purple-300/25 rounded-full animate-float"
      style="animation-delay: 1.5s"
    ></div>
  </div>

  <!-- Main Content -->
  <div class="relative z-10 min-h-screen flex items-center justify-center px-4 pt-20">
    <div class="w-full max-w-md">
      <!-- Login Title -->
      <div class="text-start mb-8">
        <h1 class="text-4xl font-black text-white">Войти на платформу</h1>
      </div>

      <!-- Login Form -->
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="form-container space-y-5">
        <!-- Error Message -->
        <div *ngIf="errorMessage" class="bg-red-500/20 border border-red-500/50 rounded-lg p-3 mb-4">
          <p class="text-red-300 text-sm">{{ errorMessage }}</p>
        </div>

        <!-- Email Field -->
        <div class="relative">
          <input
            type="email"
            placeholder="Email"
            formControlName="email"
            [class]="'input-field w-full px-4 py-2.5 bg-black/30 backdrop-blur-sm border rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:bg-black/40 transition-all ' +
                     (isFieldInvalid('email') ? 'border-red-500/50 focus:ring-red-400 focus:border-red-400' : 'border-purple-400/30 focus:ring-blue-400 focus:border-blue-400')"
          />
          <div *ngIf="getFieldError('email')" class="text-red-400 text-xs mt-1">
            {{ getFieldError('email') }}
          </div>
        </div>

        <!-- Password Field -->
        <div class="relative">
          <input
            type="password"
            placeholder="Пароль"
            formControlName="password"
            [class]="'input-field w-full px-4 py-2.5 bg-black/30 backdrop-blur-sm border rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:bg-black/40 transition-all ' +
                     (isFieldInvalid('password') ? 'border-red-500/50 focus:ring-red-400 focus:border-red-400' : 'border-purple-400/30 focus:ring-blue-400 focus:border-blue-400')"
          />
          <div *ngIf="getFieldError('password')" class="text-red-400 text-xs mt-1">
            {{ getFieldError('password') }}
          </div>
          <!-- Small inscription under password -->
          <div class="text-right mt-2">
            <a
              href="#"
              class="text-sm text-gray-300 hover:text-white transition-colors"
            >
              Забыли пароль?
            </a>
          </div>
        </div>

        <!-- Login Button -->
        <div class="pt-2">
          <button
            type="submit"
            [disabled]="isLoading"
            [class]="'login-button w-full px-6 py-3 text-white font-medium rounded-lg shadow-lg transition-all transform ' +
                     (isLoading ? 'bg-gray-600 cursor-not-allowed' : 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 hover:scale-[1.02]')"
          >
            <span *ngIf="!isLoading">Вход</span>
            <span *ngIf="isLoading" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Вход...
            </span>
          </button>
          <div class="mt-2">
            <p class="text-gray-300 text-sm text-center">
              Нет аккаунта?
              <a
                href="/registration"
                class="text-blue-400 hover:text-blue-300 transition-colors font-medium"
              >
                Зарегистрироваться
              </a>
            </p>
          </div>
        </div>

        <!-- Divider -->
        <div class="flex items-center my-2">
          <div
            class="flex-1 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"
          ></div>
          <span class="px-4 text-gray-300 text-sm">или</span>
          <div
            class="flex-1 h-px bg-gradient-to-r from-transparent via-white/30 to-transparent"
          ></div>
        </div>

        <!-- Google Login Button -->
        <button
          type="button"
          class="google-button w-full flex items-center justify-center gap-3 px-6 py-2 bg-white/90 backdrop-blur-sm border border-white/20 rounded-lg text-gray-800 font-medium shadow-lg"
        >
          <img src="/assets/icons/google.svg" alt="Google" class="w-5 h-5" />
          Войти через Google
        </button>
      </form>
    </div>
  </div>

  <!-- Bottom decorative network pattern -->
  <div class="fixed bottom-0 left-0 right-0 h-64 pointer-events-none z-5">
    <svg
      class="network-animation w-full h-full opacity-20"
      viewBox="0 0 1200 300"
      preserveAspectRatio="none"
    >
      <defs>
        <linearGradient
          id="networkGradient"
          x1="0%"
          y1="0%"
          x2="100%"
          y2="100%"
        >
          <stop offset="0%" style="stop-color: #3b82f6; stop-opacity: 0.6" />
          <stop offset="50%" style="stop-color: #7c3aed; stop-opacity: 0.4" />
          <stop offset="100%" style="stop-color: #1e40af; stop-opacity: 0.6" />
        </linearGradient>
      </defs>
      <!-- Network lines -->
      <g stroke="url(#networkGradient)" stroke-width="1" fill="none">
        <path
          d="M0,200 L200,150 L400,180 L600,120 L800,160 L1000,140 L1200,170"
        />
        <path
          d="M0,220 L150,180 L350,200 L550,160 L750,190 L950,170 L1200,200"
        />
        <path
          d="M0,240 L100,200 L300,220 L500,180 L700,210 L900,190 L1200,220"
        />
        <!-- Connecting lines -->
        <path d="M200,150 L150,180" />
        <path d="M400,180 L350,200" />
        <path d="M600,120 L550,160" />
        <path d="M800,160 L750,190" />
        <path d="M1000,140 L950,170" />
      </g>
      <!-- Network nodes -->
      <g fill="url(#networkGradient)">
        <circle cx="200" cy="150" r="3" />
        <circle cx="400" cy="180" r="3" />
        <circle cx="600" cy="120" r="3" />
        <circle cx="800" cy="160" r="3" />
        <circle cx="1000" cy="140" r="3" />
        <circle cx="150" cy="180" r="2" />
        <circle cx="350" cy="200" r="2" />
        <circle cx="550" cy="160" r="2" />
        <circle cx="750" cy="190" r="2" />
        <circle cx="950" cy="170" r="2" />
      </g>
    </svg>
  </div>
</div>

<!-- Modal Component -->
<app-modal></app-modal>

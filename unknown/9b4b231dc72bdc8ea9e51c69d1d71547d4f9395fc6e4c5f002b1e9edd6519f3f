export interface GameGalleryItem {
  id: number;
  game: number;
  file: string;
  uploaded_at: string;
}

// GameFile interface (defined before Game to avoid forward reference issues)
export interface GameFile {
  id: number;
  game: number;
  file: string;
  file_name: string;
  file_size: number;
  platform: 'windows' | 'mac' | 'linux' | 'android' | 'ios' | 'web';
  version: string;
  description?: string;
  is_active: boolean;
  download_url: string;
  uploaded_at: string;
  updated_at: string;
}

export interface Game {
  id: number;
  title: string;
  subtitle?: string;
  description: string;
  how_to_play?: string;
  target_audience?: string;
  requires_device: boolean;
  price: string;
  trial_available: boolean;
  cover_image: string | null;
  gallery_items?: GameGalleryItem[];
  game_files?: GameFile[]; // Available game files for this game
  system_requirements?: string;
  required_equipment?: string;
  created_at: string;
  game_code?: string; // Admin only field - unique 6 character code in uppercase
  is_in_cart?: boolean; // Available when user is authenticated
  is_in_library?: boolean; // Available when user is authenticated - user has ever purchased this game
  has_access?: boolean; // Available when user is authenticated - user has active access right now
  access_end?: string | null; // Available when user is authenticated - date until access is valid
  has_unactivated_access?: boolean; // Available when user is authenticated - user has 1-day access that needs activation
}

export interface CreateGameRequest {
  title: string;
  subtitle?: string;
  description: string;
  how_to_play?: string;
  target_audience?: string;
  requires_device: boolean;
  price: string;
  trial_available: boolean;
  cover_image?: File | string;
  system_requirements?: string;
  required_equipment?: string;
  game_code?: string; // Admin only field - unique 6 character code in uppercase
}

export interface CreateGalleryItemRequest {
  game: number;
  file: File;
}

export interface UpdateGameRequest extends Partial<CreateGameRequest> {}

export interface GameFilters {
  ordering?: string;
  search?: string;
}

export interface GameListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Game[];
}

export interface GameError {
  title?: string[];
  subtitle?: string[];
  description?: string[];
  how_to_play?: string[];
  target_audience?: string[];
  requires_device?: string[];
  price?: string[];
  trial_available?: boolean[];
  cover_image?: string[];
  gallery_images?: string[];
  system_requirements?: string[];
  required_equipment?: string[];
  game_code?: string[];
  non_field_errors?: string[];
}

// GameFile related interfaces
export interface CreateGameFileRequest {
  game: number;
  file: File;
  platform: 'windows' | 'mac' | 'linux' | 'android' | 'ios' | 'web';
  version?: string;
  description?: string;
}

export interface UpdateGameFileRequest {
  platform?: 'windows' | 'mac' | 'linux' | 'android' | 'ios' | 'web';
  version?: string;
  description?: string;
  is_active?: boolean;
}

export interface GameFileFilters {
  ordering?: string;
  search?: string;
  game?: number;
  platform?: string;
  is_active?: boolean;
}

export interface GameFileListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: GameFile[];
}

export interface GameFileError {
  game?: string[];
  file?: string[];
  platform?: string[];
  version?: string[];
  description?: string[];
  is_active?: string[];
  non_field_errors?: string[];
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  GamePackage,
  CreateGamePackageRequest,
  UpdateGamePackageRequest,
  GamePackageFilters,
  GamePackageListResponse,
  PackagePurchaseRequest,
  PackagePurchaseResponse,
  PackageSubscription,
  MyPackagesResponse,
  SelectGamesRequest,
  SelectGamesResponse
} from '../models/game-package.model';

@Injectable({
  providedIn: 'root'
})
export class GamePackageService {
  private apiUrl = `${environment.apiUrl}/api/game-packages`;
  private packagesApiUrl = `${environment.apiUrl}/api/packages`;
  private myPackagesUrl = `${environment.apiUrl}/api/my-packages`;

  constructor(private http: HttpClient) {}

  /**
   * Get all game packages with optional filtering and search
   * @param filters - Optional filters for ordering and search
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getGamePackages(filters?: GamePackageFilters, page?: number, pageSize?: number): Observable<GamePackageListResponse> {
    let params = new HttpParams();

    if (filters?.ordering) {
      params = params.set('ordering', filters.ordering);
    }

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<GamePackageListResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a single game package by ID
   * @param id - Game package ID
   */
  getGamePackage(id: number): Observable<GamePackage> {
    return this.http.get<GamePackage>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Create a new game package (staff only)
   * @param packageData - Game package data to create
   */
  createGamePackage(packageData: CreateGamePackageRequest): Observable<GamePackage> {
    return this.http.post<GamePackage>(`${this.apiUrl}/`, packageData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update an existing game package (staff only)
   * @param id - Game package ID to update
   * @param packageData - Updated game package data
   */
  updateGamePackage(id: number, packageData: UpdateGamePackageRequest): Observable<GamePackage> {
    return this.http.patch<GamePackage>(`${this.apiUrl}/${id}/`, packageData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Delete a game package (staff only)
   * @param id - Game package ID to delete
   */
  deleteGamePackage(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Purchase a package directly (no cart)
   * @param packageId - ID of the package to purchase
   */
  purchasePackage(packageId: number): Observable<PackagePurchaseResponse> {
    const request: PackagePurchaseRequest = { package_id: packageId };
    return this.http.post<PackagePurchaseResponse>(`${this.packagesApiUrl}/purchase/`, request).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get user's package subscriptions
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getMyPackages(page: number = 1, pageSize: number = 12): Observable<MyPackagesResponse> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('page_size', pageSize.toString());

    return this.http.get<MyPackagesResponse>(`${this.myPackagesUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Select games from a package subscription
   * @param subscriptionId - ID of the package subscription
   * @param gameIds - Array of game IDs to select
   */
  selectGamesFromPackage(subscriptionId: number, gameIds: number[]): Observable<SelectGamesResponse> {
    const request: SelectGamesRequest = {
      subscription_id: subscriptionId,
      game_ids: gameIds
    };
    return this.http.post<SelectGamesResponse>(`${this.packagesApiUrl}/select-games/`, request).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a specific package subscription by ID
   * @param subscriptionId - ID of the package subscription
   */
  getPackageSubscription(subscriptionId: number): Observable<PackageSubscription> {
    return this.http.get<PackageSubscription>(`${this.myPackagesUrl}/${subscriptionId}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400 && error.error) {
        // Validation errors
        const validationErrors = error.error;
        if (validationErrors.non_field_errors && validationErrors.non_field_errors.length > 0) {
          errorMessage = validationErrors.non_field_errors[0];
        } else {
          // Find first field error
          const firstErrorField = Object.keys(validationErrors).find(key => 
            Array.isArray(validationErrors[key]) && validationErrors[key].length > 0
          );
          if (firstErrorField) {
            errorMessage = validationErrors[firstErrorField][0];
          }
        }
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized access';
      } else if (error.status === 403) {
        errorMessage = 'Access forbidden';
      } else if (error.status === 404) {
        errorMessage = 'Game package or subscription not found';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error';
      } else if (error.error?.detail) {
        errorMessage = error.error.detail;
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}

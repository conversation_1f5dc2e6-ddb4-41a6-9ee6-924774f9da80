import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { CheckoutService } from './checkout.service';
import { Purchase } from '../models/cart.model';
import { environment } from '../../environments/environment';

describe('CheckoutService', () => {
  let service: CheckoutService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/api/checkout`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [CheckoutService]
    });
    service = TestBed.inject(CheckoutService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('checkout', () => {
    it('should process checkout successfully', () => {
      const mockPurchases: Purchase[] = [
        {
          id: 1,
          game_title: 'Call of Steppes',
          purchase_type: 'game',
          price: '5900.00',
          status: 'pending',
          created_at: '2025-06-27T12:34:56Z'
        },
        {
          id: 2,
          package_title: 'Premium Package',
          purchase_type: 'package',
          price: '2990.00',
          status: 'pending',
          created_at: '2025-06-27T12:34:56Z'
        }
      ];

      service.checkout().subscribe(purchases => {
        expect(purchases).toEqual(mockPurchases);
        expect(purchases.length).toBe(2);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({});
      req.flush(mockPurchases);
    });

    it('should handle checkout error with non_field_errors', () => {
      const errorResponse = {
        non_field_errors: ['Cart is empty']
      };

      service.checkout().subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toBe('Cart is empty');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(errorResponse, { status: 400, statusText: 'Bad Request' });
    });

    it('should handle checkout error with detail message', () => {
      const errorResponse = {
        detail: 'Authentication required'
      };

      service.checkout().subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toBe('Authentication required');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(errorResponse, { status: 401, statusText: 'Unauthorized' });
    });

    it('should handle generic error', () => {
      service.checkout().subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toBe('Server Error');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush('Server Error', { status: 500, statusText: 'Internal Server Error' });
    });
  });
});

import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GamePackageService } from '../../../core/services/game-package.service';
import { CartService } from '../../../core/services/cart.service';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { GamePackage, GamePackageListResponse, GamePackageFilters } from '../../../core/models/game-package.model';

@Component({
  selector: 'app-profile-packages-catalog',
  standalone: false,
  templateUrl: './profile-packages-catalog.component.html',
  styleUrl: './profile-packages-catalog.component.css'
})
export class ProfilePackagesCatalogComponent implements OnInit, OnDestroy {
  packages: GamePackage[] = [];
  packagesLoading = false;
  packagesError = '';

  // Pagination
  totalPackages = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-id';
  private searchSubject = new Subject<string>();

  private subscriptions: Subscription[] = [];

  constructor(
    private packageService: GamePackageService,
    private cartService: CartService,
    private authService: AuthService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadPackages();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  private setupSearch(): void {
    const searchSub = this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadPackages();
    });
    this.subscriptions.push(searchSub);
  }

  loadPackages(): void {
    this.packagesLoading = true;
    this.packagesError = '';

    const filters: GamePackageFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.packageService.getGamePackages(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.packages = response.results;
        this.totalPackages = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.packagesLoading = false;
      },
      error: (error) => {
        this.packagesError = error.message || 'Failed to load packages';
        this.packagesLoading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSortChange(): void {
    this.loadPackages();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadPackages();
  }

  /**
   * Add package to cart
   */
  addToCart(gamePackage: GamePackage): void {
    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Вход в систему', 'Для добавления в корзину необходимо войти в систему');
      return;
    }

    this.cartService.addPackageToCart(gamePackage).subscribe({
      next: () => {
        this.modalService.success('Успех', 'Пакет добавлен в корзину');
      },
      error: (error) => {
        console.error('Error adding package to cart:', error);
        this.modalService.error('Ошибка', 'Ошибка при добавлении пакета в корзину');
      }
    });
  }

  /**
   * Navigate to package details
   */
  viewPackageDetails(gamePackage: GamePackage): void {
    this.router.navigate(['/profile/packages', gamePackage.id]);
  }

  /**
   * Format price for display
   */
  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  getTotalPages(): number {
    return Math.ceil(this.totalPackages / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }
}

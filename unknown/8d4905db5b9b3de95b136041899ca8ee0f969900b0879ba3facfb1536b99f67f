import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface ActivateGameRequest {
  game_id: number;
  access_start: string; // ISO format: "2025-07-17T14:51:51.582Z"
}

export interface ActivateGameResponse {
  detail: string;
  access_start: string;
  access_end: string;
}

export interface ActivationError {
  detail?: string;
  game_id?: string[];
  access_start?: string[];
  non_field_errors?: string[];
}

@Injectable({
  providedIn: 'root'
})
export class ActivationService {
  private apiUrl = `${environment.apiUrl}/api/access/activate`;

  constructor(private http: HttpClient) {}

  /**
   * Activate 1-day game access
   * @param gameId - ID of the game to activate
   * @param accessStart - Start time for activation in ISO format
   */
  activateGame(gameId: number, accessStart: string): Observable<ActivateGameResponse> {
    const request: ActivateGameRequest = {
      game_id: gameId,
      access_start: accessStart
    };

    return this.http.post<ActivateGameResponse>(`${this.apiUrl}/`, request).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Activate game with current timestamp
   * @param gameId - ID of the game to activate
   */
  activateGameNow(gameId: number): Observable<ActivateGameResponse> {
    const now = new Date().toISOString();
    return this.activateGame(gameId, now);
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error) {
      const activationError = error.error as ActivationError;
      
      if (activationError.detail) {
        errorMessage = activationError.detail;
      } else if (activationError.non_field_errors && activationError.non_field_errors.length > 0) {
        errorMessage = activationError.non_field_errors[0];
      } else if (activationError.game_id && activationError.game_id.length > 0) {
        errorMessage = `Game ID error: ${activationError.game_id[0]}`;
      } else if (activationError.access_start && activationError.access_start.length > 0) {
        errorMessage = `Access start error: ${activationError.access_start[0]}`;
      } else if (typeof error.error === 'string') {
        errorMessage = error.error;
      }
    } else if (error.message) {
      errorMessage = error.message;
    }

    // Handle specific HTTP status codes
    switch (error.status) {
      case 400:
        if (!errorMessage || errorMessage === 'An unknown error occurred') {
          errorMessage = 'Invalid request data';
        }
        break;
      case 404:
        if (!errorMessage || errorMessage === 'An unknown error occurred') {
          errorMessage = 'Unactivated access not found. Either already activated or no entry at all.';
        }
        break;
      case 401:
        errorMessage = 'Authentication required';
        break;
      case 403:
        errorMessage = 'Access denied';
        break;
      case 500:
        errorMessage = 'Server error occurred';
        break;
    }

    console.error('Activation API Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}

<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 p-4 lg:p-6">
  <div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl lg:text-3xl font-bold text-white mb-2">Пакеты игр</h1>
      <p class="text-gray-300 text-sm lg:text-base">Выберите подходящий пакет для вашего мероприятия</p>
    </div>

    <!-- Controls -->
    <div class="bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 lg:p-6 mb-6">
      <div class="flex flex-col lg:flex-row gap-4 lg:items-center lg:justify-between">
        <!-- Search and Sort -->
        <div class="flex flex-col sm:flex-row gap-3 flex-1">
          <div class="relative flex-1 max-w-md">
            <input
              type="text"
              [(ngModel)]="searchTerm"
              (input)="onSearchChange()"
              placeholder="Поиск пакетов..."
              class="w-full bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
            <svg class="absolute right-3 top-2.5 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          
          <select
            [(ngModel)]="sortBy"
            (change)="onSortChange()"
            class="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="-id">Новые сначала</option>
            <option value="id">Старые сначала</option>
            <option value="name">Название А-Я</option>
            <option value="-name">Название Я-А</option>
            <option value="price">Цена по возрастанию</option>
            <option value="-price">Цена по убыванию</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div *ngIf="packagesLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div *ngIf="packagesError && !packagesLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <p class="text-red-400">{{ packagesError }}</p>
    </div>

    <!-- Packages Grid -->
    <div *ngIf="!packagesLoading && !packagesError" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      <div *ngFor="let package of packages" class="bg-slate-800/50 backdrop-blur-sm rounded-lg p-6 hover:bg-slate-800/70 transition-all duration-300 cursor-pointer hover:scale-105"
           (click)="viewPackageDetails(package)">
        
        <!-- Package Header -->
        <div class="text-center mb-6">
          <h3 class="text-xl font-bold text-white mb-2">{{ package.name }}</h3>
          <p class="text-gray-300 text-sm">{{ package.description }}</p>
        </div>

        <!-- Benefits -->
        <div class="space-y-3 mb-6">
          <div *ngIf="package.benefit_1" class="flex items-start gap-3">
            <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-300 text-sm">{{ package.benefit_1 }}</span>
          </div>
          <div *ngIf="package.benefit_2" class="flex items-start gap-3">
            <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-300 text-sm">{{ package.benefit_2 }}</span>
          </div>
          <div *ngIf="package.benefit_3" class="flex items-start gap-3">
            <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-300 text-sm">{{ package.benefit_3 }}</span>
          </div>
          <div class="flex items-start gap-3">
            <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-300 text-sm">{{ package.duration_days }} дней доступа</span>
          </div>
          <div class="flex items-start gap-3">
            <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="text-gray-300 text-sm">До {{ package.max_selectable_games }} игр</span>
          </div>
        </div>

        <!-- Games Info -->
        <div class="mb-6">
          <div class="flex flex-wrap gap-1 mb-3">
            <span *ngFor="let game of package.games" 
                  class="bg-blue-600/20 text-blue-400 px-2 py-1 rounded text-xs">
              {{ game.title }}
            </span>
          </div>
          <p class="text-xs text-gray-500">{{ package.games.length }} игр в пакете</p>
        </div>

        <!-- Pricing and Action -->
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-400 mb-4">{{ formatPrice(package.price) }}</div>
          <button
            (click)="addToCart(package); $event.stopPropagation()"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm font-medium transition-colors"
          >
            В корзину
          </button>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="!packagesLoading && !packagesError && packages.length === 0" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
      </svg>
      <h3 class="text-lg font-medium text-white mb-2">Пакеты не найдены</h3>
      <p class="text-gray-400">Попробуйте изменить параметры поиска.</p>
    </div>

    <!-- Pagination -->
    <div *ngIf="!packagesLoading && packages.length > 0 && getTotalPages() > 1" 
         class="flex justify-center items-center space-x-2">
      <button
        (click)="onPageChange(currentPage - 1)"
        [disabled]="!hasPrevious"
        class="px-3 py-2 text-sm font-medium text-gray-300 bg-slate-700 rounded-lg hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Назад
      </button>
      
      <button
        *ngFor="let page of getPageNumbers()"
        (click)="onPageChange(page)"
        [class.bg-blue-600]="page === currentPage"
        [class.bg-slate-700]="page !== currentPage"
        class="px-3 py-2 text-sm font-medium text-white rounded-lg hover:bg-blue-500 transition-colors"
      >
        {{ page }}
      </button>
      
      <button
        (click)="onPageChange(currentPage + 1)"
        [disabled]="!hasNext"
        class="px-3 py-2 text-sm font-medium text-gray-300 bg-slate-700 rounded-lg hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        Вперед
      </button>
    </div>
  </div>
</div>

# Games Catalog Feature

This document describes the newly implemented Games Catalog feature for the Toy-for-Toi application.

## Overview

The Games Catalog feature provides users with a comprehensive interface to browse, search, and purchase games. It includes a catalog page with filtering and pagination, detailed game pages, and shopping cart functionality.

## Features

### 1. Games Catalog Page (`/games`)
- **Grid Layout**: Responsive grid showing game cards with cover images, titles, descriptions, and prices
- **Search**: Real-time search functionality with debounced input
- **Filtering**: Sort games by date, title, or price
- **Pagination**: Navigate through multiple pages of games
- **Add to Cart**: Quick add to cart functionality from catalog view
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### 2. Game Detail Page (`/games/:id`)
- **Detailed Information**: Complete game information including description, how to play, target audience
- **Image Gallery**: Display cover image and additional gallery images
- **Technical Details**: System requirements and required equipment
- **Purchase Options**: Add to cart functionality with cart status indication
- **Navigation**: Easy navigation back to catalog

### 3. Shopping Cart System (API-Based)
- **API Integration**: Full CRUD operations with backend API
- **Authentication Required**: Cart operations require user login
- **Real-time Updates**: Cart count updates in header immediately
- **Item Management**: Add, remove, and update quantities via API
- **Price Calculation**: Automatic total calculation with game data
- **Cart Indicator**: Visual cart count badge in header
- **Cart Page**: Dedicated cart management page at `/cart`

## Technical Implementation

### Components
- `GamesCatalogComponent`: Main catalog page with search, filter, and pagination
- `GameDetailComponent`: Individual game detail page
- `CartComponent`: Dedicated cart management page
- `CartService`: Manages shopping cart state via API

### Services
- `GameService`: Existing service for game API operations
- `CartService`: API-based service for cart management with authentication

### Models
- `Game`: Existing game model with all game properties
- `Cart`: New cart model with items and totals
- `CartItem`: Individual cart item model

### Routing
- `/games` - Games catalog page
- `/games/:id` - Individual game detail page
- `/cart` - Shopping cart page (requires authentication)

## API Integration

The feature integrates with both Games and Cart APIs:

### Games API
- `GET /api/games/` - Fetch games with pagination and filtering
- `GET /api/games/:id/` - Fetch individual game details

### Cart API (Requires Authentication)
- `GET /api/cart/` - Get user's cart items
- `POST /api/cart/` - Add game to cart
- `PUT /api/cart/:id/` - Update cart item quantity
- `DELETE /api/cart/:id/` - Remove item from cart

## Navigation Updates

### Header Navigation
- Updated "Игры" link to navigate to `/games` instead of anchor link
- Added cart count badge showing number of items in cart
- Cart icon with real-time count updates

### Profile Page
- Updated "Каталог игр" link to navigate to `/games`

## Styling

### Design Consistency
- Follows existing design patterns with dark theme
- Uses consistent color scheme (blue/purple gradients)
- Maintains responsive design principles

### Animations
- Smooth hover effects on cards and buttons
- Loading spinners for API calls
- Transition animations for interactive elements

## User Experience Features

### Search & Filter
- Debounced search input (300ms delay)
- Multiple sort options (date, title, price)
- Real-time results count display

### Cart Management
- API-based cart operations with authentication
- Visual feedback when items are added to cart
- Disabled state for items already in cart
- Real-time cart updates across all pages
- Dedicated cart page for item management
- Quantity controls with API synchronization
- Error handling for API failures

### Loading States
- Loading spinners during API calls
- Error handling with retry options
- Empty state messaging

## File Structure

```
src/app/
├── core/
│   ├── models/
│   │   └── cart.model.ts          # Cart-related interfaces
│   └── services/
│       └── cart.service.ts        # Cart management service
├── features/
│   ├── games/
│   │   ├── games-catalog.component.ts     # Catalog page component
│   │   ├── games-catalog.component.html   # Catalog page template
│   │   ├── games-catalog.component.css    # Catalog page styles
│   │   ├── game-detail.component.ts       # Detail page component
│   │   ├── game-detail.component.html     # Detail page template
│   │   ├── game-detail.component.css      # Detail page styles
│   │   └── games.module.ts                # Games feature module
│   └── cart/
│       ├── cart.component.ts              # Cart page component
│       ├── cart.component.html            # Cart page template
│       ├── cart.component.css             # Cart page styles
│       └── cart.module.ts                 # Cart feature module
└── shared/
    └── components/
        └── header/
            ├── header.ts          # Updated with cart functionality
            └── header.html        # Updated with cart count badge
```

## Usage Instructions

### For Users
1. **Login required** for cart functionality
2. Navigate to "Игры" in the header or profile sidebar
3. Browse games using search and filter options
4. Click "Подробнее" to view detailed game information
5. Click "В корзину" to add games to cart (requires authentication)
6. View cart count in header navigation
7. Click "Корзина" to manage cart items
8. Update quantities or remove items from cart page

### For Developers
1. Import `GamesModule` in app module
2. Add routes for `/games` and `/games/:id`
3. Inject `CartService` where cart functionality is needed
4. Use `GameService` for API operations

## Future Enhancements

Potential improvements for the games catalog:
- Cart checkout functionality
- User reviews and ratings
- Game categories and tags
- Advanced filtering options
- Wishlist functionality
- Game recommendations
- Social sharing features

## Dependencies

The feature uses existing dependencies:
- Angular Router for navigation
- Angular Forms for search input
- RxJS for reactive programming
- Existing game service and models

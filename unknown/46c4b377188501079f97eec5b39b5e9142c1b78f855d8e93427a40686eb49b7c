# Game Activation Feature

This document describes the implementation of the game activation feature for 1-day access in the TOY FOR TOI application.

## Overview

The game activation feature allows users to activate 1-day game access that they have purchased but not yet activated. When users purchase a game with 1-day access, they receive an unactivated access that they can activate at any time. Once activated, the 24-hour countdown begins.

## API Integration

### Activation Endpoint
- **POST** `/api/access/activate/`
- **Authorization**: Bearer token required
- **Request Body**:
  ```json
  {
    "game_id": 5,
    "access_start": "2025-07-17T14:51:51.582Z"
  }
  ```

### Response Format
```json
{
  "detail": "Game access activated successfully",
  "access_start": "2025-07-17T14:51:51.582Z",
  "access_end": "2025-07-18T14:51:51.582Z"
}
```

### Error Handling
- **400 Bad Request**: Missing or invalid parameters
  - `The game_id field is required.`
  - `The access_start field is required.`
  - `Invalid date format.` (Non-ISO format)
- **404 Not Found**: `Unactivated access not found. Either already activated or no entry at all.`

## Game Model Updates

### New Field
Added `has_unactivated_access` field to the Game model:

```typescript
export interface Game {
  // ... existing fields
  has_unactivated_access?: boolean; // Available when user is authenticated - user has 1-day access that needs activation
}
```

## Service Implementation

### ActivationService
New service created at `src/app/core/services/activation.service.ts`:

**Key Methods:**
- `activateGame(gameId: number, accessStart: string)`: Activate game with specific start time
- `activateGameNow(gameId: number)`: Activate game with current timestamp
- Comprehensive error handling for all API error scenarios

## Component Updates

### ProfileGameDetailComponent
- Added activation functionality for library game detail pages
- Shows activation button when `canActivate()` returns true
- Displays "Доступ на 1 день готов к активации" status
- Confirmation modal before activation
- Success message with access end time

### GameDetailComponent (Main)
- Same activation functionality as profile component
- Consistent UI/UX across both game detail views

### Games Catalog Components
- Updated status indicators to show unactivated access
- Yellow badge for "Активировать" status
- Updated logic to differentiate between expired and unactivated access

### User Library Component
- Shows unactivated access status in library grid
- Yellow "Активировать" badge
- Detailed access information panel with activation prompt

## UI/UX Features

### Status Indicators
- **Green**: Active access - "Играть" / "Доступ активен"
- **Yellow**: Unactivated access - "Активировать" / "Готов к активации"
- **Red**: Expired access - "Истёк" / "Доступ истёк"

### Activation Button
- Yellow gradient styling to match unactivated status
- Loading state with spinner during activation
- Disabled state during processing
- Star icon to indicate special action

### Confirmation Flow
1. User clicks "Активировать доступ" button
2. Confirmation modal appears with game title and warning about countdown
3. User confirms activation
4. API call made with current timestamp
5. Success message shows activation details and end time
6. Game status updates immediately in UI

## Logic Updates

### Access Status Methods
Updated all components with new logic:

```typescript
hasUnactivatedAccess(game: Game): boolean {
  return game.has_unactivated_access || false;
}

canActivate(): boolean {
  return this.isInLibrary() && this.hasUnactivatedAccess() && !this.hasAccess();
}

needsAccessExtension(): boolean {
  return this.isInLibrary() && !this.hasAccess() && !this.hasUnactivatedAccess();
}
```

## Payment Flow Integration

### Purchase Process
1. User purchases game with 1-day access type
2. Payment API sets `has_unactivated_access: true` for the game
3. User sees yellow "Активировать" status in all game views
4. User can activate at any time from game detail pages

### Activation Process
1. User navigates to `/profile/library/games/:id` or `/games/:id`
2. Sees activation button if `canActivate()` is true
3. Clicks activation button
4. Confirms in modal dialog
5. API activates access with current timestamp
6. Game status updates to active access
7. 24-hour countdown begins

## Testing

### Unit Tests
- ActivationService tests cover all API scenarios
- Error handling tests for all error types
- Success flow tests with proper request/response validation

### Integration Points
- Works with existing payment flow
- Integrates with game status display logic
- Compatible with cart and library systems

## File Changes

### New Files
- `src/app/core/services/activation.service.ts`
- `src/app/core/services/activation.service.spec.ts`

### Modified Files
- `src/app/core/models/game.model.ts` - Added `has_unactivated_access` field
- `src/app/features/profile/components/profile-game-detail.component.ts` - Added activation methods
- `src/app/features/profile/components/profile-game-detail.component.html` - Added activation UI
- `src/app/features/games/game-detail.component.ts` - Added activation methods
- `src/app/features/games/game-detail.component.html` - Added activation UI
- `src/app/features/games/games-catalog.component.ts` - Added status methods
- `src/app/features/games/games-catalog.component.html` - Updated status display
- `src/app/features/profile/components/profile-games-catalog.component.ts` - Added status methods
- `src/app/features/profile/components/user-library/user-library.component.ts` - Added status methods
- `src/app/features/profile/components/user-library/user-library.component.html` - Updated status display
- `src/app/features/main/main.ts` - Added status methods

## Future Enhancements

1. **Scheduled Activation**: Allow users to schedule activation for a future time
2. **Activation History**: Track when games were activated
3. **Bulk Activation**: Activate multiple games at once
4. **Activation Reminders**: Notify users about unactivated access
5. **Access Extension**: Allow extending active access before expiration

# Game Keys Management Feature

This document describes the Game Keys Management feature implementation for the TOY FOR TOI application.

## Overview

The Game Keys Management feature allows staff users to manage game activation keys through a comprehensive admin interface. This includes creating, viewing, filtering, and deleting game keys with full API integration.

## API Endpoints

The feature integrates with the following backend API endpoints:

### GET /api/game-keys/
Retrieves a paginated list of game keys with optional filtering.

**Response Format:**
```json
{
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
        {
            "id": 1,
            "game": 1,
            "game_title": "Game Title",
            "code": "NEWKEY-2025-XYZ",
            "is_used": false,
            "assigned_to_user": null,
            "assigned_at": null,
            "expires_at": null
        }
    ]
}
```

### POST /api/game-keys/
Creates a new game key.

**Request Format:**
```json
{
    "game": 1,
    "code": "NEWKEY-2025-XYZ",
    "expires_at": "2025-12-31T23:59:59Z" // Optional
}
```

### DELETE /api/game-keys/{id}/
Deletes a specific game key (staff only).

## Features

### 1. Game Keys List View
- **Paginated Display**: Shows game keys in a responsive table format
- **Real-time Search**: Search by game key code or game title
- **Advanced Filtering**: Filter by game, usage status (used/unused)
- **Sorting Options**: Sort by ID, game title, code, or usage status
- **Status Indicators**: Visual indicators for used/unused keys

### 2. Game Key Creation
- **Inline Form**: Add new game keys without leaving the page
- **Game Selection**: Dropdown to select from available games
- **Code Input**: Manual entry of activation codes
- **Expiration Date**: Optional expiration date setting
- **Validation**: Client-side and server-side validation
- **Error Handling**: Comprehensive error display and handling

### 3. Game Key Management
- **Delete Functionality**: Remove game keys with confirmation
- **Assignment Tracking**: View which user a key is assigned to
- **Usage Status**: Track whether keys have been used
- **Expiration Monitoring**: Display expiration dates

## Technical Implementation

### Components
- `GameKeysManagementComponent`: Main management interface
- Located at: `src/app/features/profile/components/game-keys-management/`

### Services
- `GameKeyService`: API integration service for game keys operations
- Located at: `src/app/core/services/game-key.service.ts`

### Models
- `GameKey`: Interface for game key objects
- `GameKeyListResponse`: Paginated response interface
- `CreateGameKeyRequest`: Request interface for creating keys
- `GameKeyFilters`: Filtering options interface
- `GameKeyError`: Error handling interface
- Located at: `src/app/core/models/game-key.model.ts`

### Integration
- **Profile Module**: Integrated into the admin section of the profile page
- **Navigation**: Accessible via "Управление ключами игр" in the admin sidebar
- **Authentication**: Requires staff privileges to access
- **Responsive Design**: Works on desktop and mobile devices

## File Structure

```
src/app/
├── core/
│   ├── models/
│   │   └── game-key.model.ts           # Game key interfaces
│   └── services/
│       └── game-key.service.ts         # Game key API service
└── features/
    └── profile/
        ├── components/
        │   └── game-keys-management/
        │       ├── game-keys-management.component.ts    # Component logic
        │       ├── game-keys-management.component.html  # Template
        │       └── game-keys-management.component.css   # Styles
        ├── profile.html                 # Updated with game keys navigation
        ├── profile.ts                   # Updated with game keys section
        └── profile.module.ts            # Updated with component declaration
```

## Usage Instructions

### For Administrators
1. **Access**: Navigate to Profile → Администрирование → Управление ключами игр
2. **View Keys**: Browse existing game keys with pagination
3. **Search**: Use the search bar to find specific keys
4. **Filter**: Apply filters by game or usage status
5. **Add Keys**: Click "Добавить ключ" to create new activation keys
6. **Delete Keys**: Use the delete button to remove unwanted keys

### For Developers
1. **Import Service**: Inject `GameKeyService` where needed
2. **Use Models**: Import interfaces from `game-key.model.ts`
3. **Error Handling**: Handle API errors using the service's error handling
4. **Pagination**: Use the built-in pagination support

## API Integration Details

### Authentication
- All endpoints require JWT authentication
- Staff privileges required for create/delete operations
- Uses the existing `AuthInterceptor` for token management

### Error Handling
- Comprehensive error handling for network issues
- Validation error display for form submissions
- User-friendly error messages in Russian

### Pagination
- Supports standard Django REST framework pagination
- Configurable page size (default: 12 items per page)
- Navigation controls for large datasets

## Styling and Design

### Design Consistency
- Follows existing design patterns with dark theme
- Uses consistent color scheme (blue/purple gradients)
- Maintains responsive design principles
- Consistent with other admin components

### User Experience
- Loading states for all API operations
- Confirmation dialogs for destructive actions
- Success/error notifications using the modal service
- Intuitive form validation and feedback

## Future Enhancements

Potential improvements for the game keys feature:

1. **Bulk Operations**: Add/delete multiple keys at once
2. **Key Generation**: Automatic key generation with patterns
3. **Export Functionality**: Export keys to CSV/Excel
4. **Usage Analytics**: Track key usage statistics
5. **Batch Assignment**: Assign keys to multiple users
6. **Key Templates**: Predefined key formats for different games
7. **Audit Trail**: Track all key-related actions
8. **Email Integration**: Send keys to users via email

## Dependencies

The feature relies on the following existing services and components:
- `AuthService`: For authentication and authorization
- `GameService`: For loading available games
- `ModalService`: For confirmations and notifications
- `SharedModule`: For common UI components
- Angular Forms: For form handling and validation
- Angular HTTP Client: For API communication

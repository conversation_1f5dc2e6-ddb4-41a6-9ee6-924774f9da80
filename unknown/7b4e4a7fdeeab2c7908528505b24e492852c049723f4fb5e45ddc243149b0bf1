/* Purchase History Component Styles */

/* Custom scrollbar for the component */
:host {
  display: block;
  height: 100%;
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition: all 0.3s ease;
}

/* Custom focus styles for form elements */
input:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading spinner animation */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover effects for purchase cards */
.purchase-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Status badge animations */
.status-badge {
  transition: all 0.2s ease;
}

/* Payment button hover effects */
.payment-button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

/* Pagination button styles */
.pagination-button {
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .purchase-card {
    padding: 1rem;
  }
  
  .purchase-info {
    margin-bottom: 1rem;
  }
  
  .purchase-actions {
    width: 100%;
    justify-content: center;
  }
}

/* Custom styles for status indicators */
.status-paid {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.status-pending {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.status-failed {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

/* Search and filter section styling */
.filters-section {
  backdrop-filter: blur(10px);
  background: rgba(30, 41, 59, 0.6);
}

/* Empty state styling */
.empty-state {
  backdrop-filter: blur(10px);
  background: rgba(30, 41, 59, 0.4);
}

/* Error state styling */
.error-state {
  backdrop-filter: blur(10px);
  background: rgba(127, 29, 29, 0.2);
}

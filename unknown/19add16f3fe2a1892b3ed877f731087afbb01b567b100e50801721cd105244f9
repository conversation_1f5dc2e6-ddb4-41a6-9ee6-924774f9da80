import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { Purchase, CheckoutError } from '../models/cart.model';

@Injectable({
  providedIn: 'root'
})
export class CheckoutService {
  private apiUrl = `${environment.apiUrl}/api/checkout`;

  constructor(private http: HttpClient) {}

  /**
   * Process checkout for all items in user's cart
   * Creates purchases and clears the cart
   */
  checkout(): Observable<Purchase[]> {
    return this.http.post<Purchase[]>(`${this.apiUrl}/`, {}).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Произошла неизвестная ошибка';

    if (error.error) {
      if (error.error.non_field_errors && error.error.non_field_errors.length > 0) {
        errorMessage = error.error.non_field_errors[0];
      } else if (error.error.detail) {
        errorMessage = error.error.detail;
      } else if (typeof error.error === 'string') {
        errorMessage = error.error;
      }
    }

    console.error('Checkout error:', error);
    return throwError(() => new Error(errorMessage));
  }
}

import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GameService } from '../../../core/services/game.service';
import { CartService } from '../../../core/services/cart.service';
import { AuthService } from '../../../core/services/auth.service';
import { ModalService } from '../../../core/services/modal.service';
import { Game, GameListResponse, GameFilters } from '../../../core/models/game.model';
import { Cart } from '../../../core/models/cart.model';

@Component({
  selector: 'app-profile-games-catalog',
  standalone: false,
  templateUrl: './profile-games-catalog.component.html',
  styleUrl: './profile-games-catalog.component.css'
})
export class ProfileGamesCatalogComponent implements OnInit, OnDestroy {
  games: Game[] = [];
  loading = false;
  error = '';

  // Pagination
  currentPage = 1;
  totalPages = 1;
  totalGames = 0;
  pageSize = 12;

  // Search and filtering
  searchTerm = '';
  selectedCategory = '';
  sortBy = 'title';

  // Cart data
  cart: Cart = { items: [], total_items: 0, total_price: 0 };

  // Search debouncing
  private searchSubject = new Subject<string>();
  private searchSubscription?: Subscription;
  private cartSubscription?: Subscription;
  private cartChangeSubscription?: Subscription;

  constructor(
    private gameService: GameService,
    private cartService: CartService,
    private authService: AuthService,
    private modalService: ModalService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadGames();
    this.setupCartSubscription();
    this.setupSearch();
  }

  ngOnDestroy(): void {
    this.searchSubscription?.unsubscribe();
    this.cartSubscription?.unsubscribe();
    this.cartChangeSubscription?.unsubscribe();
  }

  private setupSearch(): void {
    this.searchSubscription = this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadGames();
    });
  }

  private setupCartSubscription(): void {
    // Subscribe to cart updates
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cart = cart;
    });

    // Subscribe to cart changes to update game states
    this.cartChangeSubscription = this.cartService.cartChanges$.subscribe((change: any) => {
      if (change) {
        const game = this.games.find(g => g.id === change.gameId);
        if (game) {
          game.is_in_cart = change.action === 'added';
        }
      }
    });
  }

  loadGames(): void {
    this.loading = true;
    this.error = '';

    const filters: GameFilters = {};

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.sortBy) {
      filters.ordering = this.sortBy;
    }

    this.gameService.getGames(filters, this.currentPage, this.pageSize).subscribe({
      next: (response: GameListResponse) => {
        this.games = response.results;
        this.totalGames = response.count;
        this.totalPages = Math.ceil(response.count / this.pageSize);
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить игры';
        this.loading = false;
      }
    });
  }

  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onSearch(): void {
    this.currentPage = 1;
    this.loadGames();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.loadGames();
    }
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadGames();
  }

  viewGameDetails(gameId: number): void {
    this.router.navigate(['/profile/games', gameId]);
  }

  addToCart(game: Game, event: Event): void {
    event.stopPropagation(); // Prevent navigation to details

    // Allow adding to cart if game is in library but access has expired (for access extension)
    if (this.isInLibrary(game) && this.hasActiveAccess(game)) {
      this.modalService.error('Игра уже в библиотеке', 'У вас уже есть активный доступ к этой игре');
      return;
    }

    this.cartService.addToCart(game).subscribe({
      next: () => {
        // Success - cart will be automatically updated via subscription
        // Update the local game object to reflect the cart status immediately
        game.is_in_cart = true;
        console.log('Game added to cart successfully');
      },
      error: (error) => {
        console.error('Error adding game to cart:', error.message);
        this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
      }
    });
  }

  isInCart(game: Game): boolean {
    return game.is_in_cart || this.cart.items.some(item => item.game === game.id);
  }

  isInLibrary(game: Game): boolean {
    return game.is_in_library || false;
  }

  canPlay(game: Game): boolean {
    return game.has_access || false;
  }

  hasActiveAccess(game: Game): boolean {
    return game.has_access || false;
  }

  hasUnactivatedAccess(game: Game): boolean {
    return game.has_unactivated_access || false;
  }

  needsAccessExtension(game: Game): boolean {
    return (game.is_in_library || false) && !(game.has_access || false) && !this.hasUnactivatedAccess(game);
  }

  getPages(): number[] {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(this.totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }
}

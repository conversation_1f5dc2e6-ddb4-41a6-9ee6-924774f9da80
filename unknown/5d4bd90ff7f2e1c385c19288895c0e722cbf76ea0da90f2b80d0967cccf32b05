import { Component, Input, Output, EventEmitter } from '@angular/core';
import { GamePackage } from '../../../core/models/game-package.model';

@Component({
  selector: 'app-tarrifs-card',
  standalone: false,
  templateUrl: './tarrifs-card.html',
  styleUrl: './tarrifs-card.css'
})
export class TarrifsCard {
  @Input() package: GamePackage | null = null;
  @Input() isAuthenticated: boolean = false;
  @Output() purchasePackage = new EventEmitter<GamePackage>();
  @Output() viewDetails = new EventEmitter<GamePackage>();

  onPurchasePackage(): void {
    if (this.package) {
      this.purchasePackage.emit(this.package);
    }
  }

  onViewDetails(): void {
    if (this.package) {
      this.viewDetails.emit(this.package);
    }
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }
}

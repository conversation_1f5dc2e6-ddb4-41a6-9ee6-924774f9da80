import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { GameFileService } from './game-file.service';
import { GameFile, CreateGameFileRequest, UpdateGameFileRequest, GameFileFilters, GameFileListResponse } from '../models/game.model';
import { environment } from '../../environments/environment';

describe('GameFileService', () => {
  let service: GameFileService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/api/game-files`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [GameFileService]
    });
    service = TestBed.inject(GameFileService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getGameFiles', () => {
    it('should fetch game files without filters', () => {
      const mockResponse: GameFileListResponse = {
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            id: 1,
            game: 1,
            file: '/media/game_files/1/windows/MyGame.exe',
            file_name: 'MyGame.exe',
            file_size: 1024000,
            platform: 'windows',
            version: '1.0',
            description: 'Windows executable',
            is_active: true,
            download_url: 'http://localhost:8000/api/game-files/1/download/',
            uploaded_at: '2025-07-20T23:00:00Z',
            updated_at: '2025-07-20T23:00:00Z'
          }
        ]
      };

      service.getGameFiles().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should fetch game files with filters', () => {
      const filters: GameFileFilters = {
        search: 'test',
        game: 1,
        platform: 'windows',
        is_active: true,
        ordering: '-id'
      };

      service.getGameFiles(filters, 1, 10).subscribe();

      const req = httpMock.expectOne(request => 
        request.url === `${apiUrl}/` && 
        request.params.get('search') === 'test' &&
        request.params.get('game') === '1' &&
        request.params.get('platform') === 'windows' &&
        request.params.get('is_active') === 'true' &&
        request.params.get('ordering') === '-id' &&
        request.params.get('page') === '1' &&
        request.params.get('page_size') === '10'
      );
      expect(req.request.method).toBe('GET');
      req.flush({ count: 0, next: null, previous: null, results: [] });
    });
  });

  describe('getGameFile', () => {
    it('should fetch a single game file', () => {
      const mockGameFile: GameFile = {
        id: 1,
        game: 1,
        file: '/media/game_files/1/windows/MyGame.exe',
        file_name: 'MyGame.exe',
        file_size: 1024000,
        platform: 'windows',
        version: '1.0',
        description: 'Windows executable',
        is_active: true,
        download_url: 'http://localhost:8000/api/game-files/1/download/',
        uploaded_at: '2025-07-20T23:00:00Z',
        updated_at: '2025-07-20T23:00:00Z'
      };

      service.getGameFile(1).subscribe(gameFile => {
        expect(gameFile).toEqual(mockGameFile);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('GET');
      req.flush(mockGameFile);
    });
  });

  describe('createGameFile', () => {
    it('should create a game file with FormData', () => {
      const file = new File(['test'], 'test.exe', { type: 'application/octet-stream' });
      const gameFileData: CreateGameFileRequest = {
        game: 1,
        file: file,
        platform: 'windows',
        version: '1.0',
        description: 'Test file'
      };

      const mockResponse: GameFile = {
        id: 1,
        game: 1,
        file: '/media/game_files/1/windows/test.exe',
        file_name: 'test.exe',
        file_size: 4,
        platform: 'windows',
        version: '1.0',
        description: 'Test file',
        is_active: true,
        download_url: 'http://localhost:8000/api/game-files/1/download/',
        uploaded_at: '2025-07-20T23:00:00Z',
        updated_at: '2025-07-20T23:00:00Z'
      };

      service.createGameFile(gameFileData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body instanceof FormData).toBeTruthy();
      
      const formData = req.request.body as FormData;
      expect(formData.get('game')).toBe('1');
      expect(formData.get('platform')).toBe('windows');
      expect(formData.get('version')).toBe('1.0');
      expect(formData.get('description')).toBe('Test file');
      expect(formData.get('file')).toBe(file);
      
      req.flush(mockResponse);
    });
  });

  describe('updateGameFile', () => {
    it('should update a game file', () => {
      const updateData: UpdateGameFileRequest = {
        platform: 'mac',
        version: '1.1',
        is_active: false
      };

      const mockResponse: GameFile = {
        id: 1,
        game: 1,
        file: '/media/game_files/1/mac/MyGame.app',
        file_name: 'MyGame.app',
        file_size: 1024000,
        platform: 'mac',
        version: '1.1',
        description: 'macOS application',
        is_active: false,
        download_url: 'http://localhost:8000/api/game-files/1/download/',
        uploaded_at: '2025-07-20T23:00:00Z',
        updated_at: '2025-07-20T23:00:00Z'
      };

      service.updateGameFile(1, updateData).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('PATCH');
      expect(req.request.body).toEqual(updateData);
      req.flush(mockResponse);
    });
  });

  describe('deleteGameFile', () => {
    it('should delete a game file', () => {
      service.deleteGameFile(1).subscribe();

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('downloadGameFile', () => {
    it('should download a game file as blob', () => {
      const mockBlob = new Blob(['test content'], { type: 'application/octet-stream' });

      service.downloadGameFile(1).subscribe(blob => {
        expect(blob).toEqual(mockBlob);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/download/`);
      expect(req.request.method).toBe('GET');
      expect(req.request.responseType).toBe('blob');
      req.flush(mockBlob);
    });
  });

  describe('getGameFilesByGame', () => {
    it('should fetch game files for a specific game', () => {
      const mockResponse: GameFileListResponse = {
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            id: 1,
            game: 1,
            file: '/media/game_files/1/windows/MyGame.exe',
            file_name: 'MyGame.exe',
            file_size: 1024000,
            platform: 'windows',
            version: '1.0',
            description: 'Windows executable',
            is_active: true,
            download_url: 'http://localhost:8000/api/game-files/1/download/',
            uploaded_at: '2025-07-20T23:00:00Z',
            updated_at: '2025-07-20T23:00:00Z'
          }
        ]
      };

      service.getGameFilesByGame(1).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(request => 
        request.url === `${apiUrl}/` && 
        request.params.get('game') === '1'
      );
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('triggerFileDownload', () => {
    it('should trigger file download in browser', () => {
      const mockBlob = new Blob(['test content'], { type: 'application/octet-stream' });
      const fileName = 'test.exe';

      // Mock URL.createObjectURL and URL.revokeObjectURL
      const mockUrl = 'blob:http://localhost/test';
      spyOn(window.URL, 'createObjectURL').and.returnValue(mockUrl);
      spyOn(window.URL, 'revokeObjectURL');

      // Mock document.createElement and appendChild/removeChild
      const mockLink = document.createElement('a');
      spyOn(document, 'createElement').and.returnValue(mockLink);
      spyOn(document.body, 'appendChild');
      spyOn(document.body, 'removeChild');
      spyOn(mockLink, 'click');

      service.triggerFileDownload(1, fileName).subscribe();

      const req = httpMock.expectOne(`${apiUrl}/1/download/`);
      expect(req.request.method).toBe('GET');
      req.flush(mockBlob);

      expect(window.URL.createObjectURL).toHaveBeenCalledWith(mockBlob);
      expect(mockLink.href).toBe(mockUrl);
      expect(mockLink.download).toBe(fileName);
      expect(document.body.appendChild).toHaveBeenCalledWith(mockLink);
      expect(mockLink.click).toHaveBeenCalled();
      expect(document.body.removeChild).toHaveBeenCalledWith(mockLink);
      expect(window.URL.revokeObjectURL).toHaveBeenCalledWith(mockUrl);
    });
  });

  describe('error handling', () => {
    it('should handle HTTP errors', () => {
      service.getGameFiles().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error).toBeTruthy();
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });

    it('should handle validation errors', () => {
      const validationError = {
        file: ['This field is required.'],
        platform: ['Invalid platform choice.']
      };

      service.getGameFiles().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error).toEqual(validationError);
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(validationError, { status: 400, statusText: 'Bad Request' });
    });
  });
});

import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  Purchase,
  PurchaseResponse,
  PaymentRequest,
  PaymentResponse,
  PaymentError,
  PurchaseFilters
} from '../models/cart.model';

@Injectable({
  providedIn: 'root'
})
export class PurchaseService {
  private apiUrl = `${environment.apiUrl}/api/purchases`;

  constructor(private http: HttpClient) {}

  /**
   * Get user's purchase history with pagination and filtering
   */
  getPurchases(filters: PurchaseFilters = {}, page: number = 1, pageSize: number = 12): Observable<PurchaseResponse> {
    let params: any = {
      page: page.toString(),
      page_size: pageSize.toString()
    };

    // Add filters to params
    if (filters.status) {
      params.status = filters.status;
    }
    if (filters.ordering) {
      params.ordering = filters.ordering;
    }
    if (filters.search) {
      params.search = filters.search;
    }

    return this.http.get<PurchaseResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Pay for a specific purchase with access type selection
   */
  payForPurchase(purchaseId: number, accessType: 'oneday' | 'subscription'): Observable<PaymentResponse> {
    const request: PaymentRequest = { access_type: accessType };
    return this.http.post<PaymentResponse>(`${this.apiUrl}/${purchaseId}/pay/`, request).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get pending purchases (helper method)
   */
  getPendingPurchases(page: number = 1, pageSize: number = 12): Observable<PurchaseResponse> {
    return this.getPurchases({ status: 'pending', ordering: '-created_at' }, page, pageSize);
  }

  /**
   * Get paid purchases (helper method)
   */
  getPaidPurchases(page: number = 1, pageSize: number = 12): Observable<PurchaseResponse> {
    return this.getPurchases({ status: 'paid', ordering: '-created_at' }, page, pageSize);
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Произошла неизвестная ошибка';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Ошибка: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 404) {
        errorMessage = 'Покупка не найдена или не принадлежит пользователю';
      } else if (error.status === 400) {
        if (error.error?.detail) {
          errorMessage = error.error.detail;
        } else {
          errorMessage = 'Покупка уже оплачена';
        }
      } else if (error.status === 401) {
        errorMessage = 'Необходима авторизация';
      } else if (error.status === 403) {
        errorMessage = 'Доступ запрещен';
      } else if (error.error?.detail) {
        errorMessage = error.error.detail;
      } else if (error.error?.non_field_errors && error.error.non_field_errors.length > 0) {
        errorMessage = error.error.non_field_errors[0];
      } else {
        errorMessage = `Ошибка сервера: ${error.status}`;
      }
    }

    return throwError(() => new Error(errorMessage));
  }
}

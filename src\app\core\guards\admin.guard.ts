import { Injectable } from '@angular/core';
import { CanActivate, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root'
})
export class AdminGuard implements CanActivate {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean> {
    return this.authService.getUserProfile().pipe(
      map(profile => {
        if (profile && profile.is_staff) {
          return true;
        } else {
          // Redirect to profile page if not admin
          this.router.navigate(['/profile']);
          return false;
        }
      }),
      catchError(() => {
        // Redirect to login if not authenticated
        this.router.navigate(['/login']);
        return of(false);
      })
    );
  }
}

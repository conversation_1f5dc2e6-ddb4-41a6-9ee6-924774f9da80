import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ModalConfig {
  title: string;
  message: string;
  type: 'confirm' | 'alert' | 'error' | 'success';
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
}

export interface ModalResult {
  confirmed: boolean;
  dismissed: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ModalService {
  private modalSubject = new BehaviorSubject<ModalConfig | null>(null);
  private resultSubject = new BehaviorSubject<ModalResult | null>(null);

  public modal$ = this.modalSubject.asObservable();
  public result$ = this.resultSubject.asObservable();

  /**
   * Show a confirmation modal
   */
  confirm(title: string, message: string, confirmText: string = 'Да', cancelText: string = 'Отмена'): Promise<boolean> {
    const config: ModalConfig = {
      title,
      message,
      type: 'confirm',
      confirmText,
      cancelText,
      showCancel: true
    };

    this.modalSubject.next(config);

    return new Promise((resolve) => {
      const subscription = this.result$.subscribe(result => {
        if (result !== null) {
          subscription.unsubscribe();
          this.resultSubject.next(null);
          resolve(result.confirmed);
        }
      });
    });
  }

  /**
   * Show an alert modal
   */
  alert(title: string, message: string, confirmText: string = 'OK'): Promise<void> {
    const config: ModalConfig = {
      title,
      message,
      type: 'alert',
      confirmText,
      showCancel: false
    };

    this.modalSubject.next(config);

    return new Promise((resolve) => {
      const subscription = this.result$.subscribe(result => {
        if (result !== null) {
          subscription.unsubscribe();
          this.resultSubject.next(null);
          resolve();
        }
      });
    });
  }

  /**
   * Show an error modal
   */
  error(title: string, message: string, confirmText: string = 'OK'): Promise<void> {
    const config: ModalConfig = {
      title,
      message,
      type: 'error',
      confirmText,
      showCancel: false
    };

    this.modalSubject.next(config);

    return new Promise((resolve) => {
      const subscription = this.result$.subscribe(result => {
        if (result !== null) {
          subscription.unsubscribe();
          this.resultSubject.next(null);
          resolve();
        }
      });
    });
  }

  /**
   * Show a success modal
   */
  success(title: string, message: string, confirmText: string = 'OK'): Promise<void> {
    const config: ModalConfig = {
      title,
      message,
      type: 'success',
      confirmText,
      showCancel: false
    };

    this.modalSubject.next(config);

    return new Promise((resolve) => {
      const subscription = this.result$.subscribe(result => {
        if (result !== null) {
          subscription.unsubscribe();
          this.resultSubject.next(null);
          resolve();
        }
      });
    });
  }

  /**
   * Confirm the modal action
   */
  confirmModal(): void {
    this.modalSubject.next(null);
    this.resultSubject.next({ confirmed: true, dismissed: false });
  }

  /**
   * Cancel/dismiss the modal
   */
  dismissModal(): void {
    this.modalSubject.next(null);
    this.resultSubject.next({ confirmed: false, dismissed: true });
  }

  /**
   * Close the modal (same as dismiss for alerts)
   */
  closeModal(): void {
    this.modalSubject.next(null);
    this.resultSubject.next({ confirmed: true, dismissed: false });
  }
}

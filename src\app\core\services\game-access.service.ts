import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { 
  GameAccess, 
  GameAccessListResponse, 
  CreateGameAccessRequest, 
  UpdateGameAccessRequest, 
  GameAccessFilters 
} from '../models/game-access.model';

@Injectable({
  providedIn: 'root'
})
export class GameAccessService {
  private apiUrl = `${environment.apiUrl}/api/admin/game-access`;

  constructor(private http: HttpClient) {}

  /**
   * Get all game access records with optional filtering and pagination
   * @param filters - Optional filters for search and filtering
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getGameAccess(filters?: GameAccessFilters, page?: number, pageSize?: number): Observable<GameAccessListResponse> {
    let params = new HttpParams();

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (filters?.user) {
      params = params.set('user', filters.user.toString());
    }

    if (filters?.game) {
      params = params.set('game', filters.game.toString());
    }

    if (filters?.access_type) {
      params = params.set('access_type', filters.access_type);
    }

    if (filters?.has_access !== undefined) {
      params = params.set('has_access', filters.has_access.toString());
    }

    if (filters?.ordering) {
      params = params.set('ordering', filters.ordering);
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<GameAccessListResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a single game access record by ID
   * @param id - Game access ID
   */
  getGameAccessById(id: number): Observable<GameAccess> {
    return this.http.get<GameAccess>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Create a new game access record (staff only)
   * @param gameAccessData - Game access data to create
   */
  createGameAccess(gameAccessData: CreateGameAccessRequest): Observable<GameAccess> {
    return this.http.post<GameAccess>(`${this.apiUrl}/`, gameAccessData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update an existing game access record (staff only)
   * @param id - Game access ID to update
   * @param gameAccessData - Updated game access data
   */
  updateGameAccess(id: number, gameAccessData: UpdateGameAccessRequest): Observable<GameAccess> {
    return this.http.patch<GameAccess>(`${this.apiUrl}/${id}/`, gameAccessData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Delete a game access record (staff only)
   * @param id - Game access ID to delete
   */
  deleteGameAccess(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400 && error.error) {
        // Handle validation errors
        if (typeof error.error === 'object') {
          const errors = Object.values(error.error).flat();
          errorMessage = errors.join(', ');
        } else {
          errorMessage = error.error;
        }
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized access. Please login again.';
      } else if (error.status === 403) {
        errorMessage = 'Access forbidden. You do not have permission to perform this action.';
      } else if (error.status === 404) {
        errorMessage = 'Game access record not found.';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error. Please try again later.';
      } else {
        errorMessage = `Error ${error.status}: ${error.message}`;
      }
    }
    
    console.error('GameAccessService Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}

export interface GamePackage {
  id: number;
  name: string;
  description: string;
  benefit_1: string;
  benefit_2: string;
  benefit_3: string;
  price: string;
  duration_days: number;
  games: GamePackageGame[];
  max_selectable_games: number;
  has_active_subscription?: boolean; // User has active subscription to THIS package
  user_has_any_active_subscription?: boolean; // User has active subscription to ANY package
  created_at?: string;
}

export interface GamePackageGame {
  id: number;
  title: string;
  cover_image?: string | null;
}

export interface GamePackageListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: GamePackage[];
}

export interface CreateGamePackageRequest {
  name: string;
  description: string;
  benefit_1?: string;
  benefit_2?: string;
  benefit_3?: string;
  price: string;
  duration_days: number;
  game_ids: number[];
  max_selectable_games: number;
}

export interface UpdateGamePackageRequest extends Partial<CreateGamePackageRequest> {}

export interface GamePackageFilters {
  ordering?: string;
  search?: string;
}

export interface GamePackageError {
  name?: string[];
  description?: string[];
  benefit_1?: string[];
  benefit_2?: string[];
  benefit_3?: string[];
  price?: string[];
  duration_days?: string[];
  game_ids?: string[];
  max_selectable_games?: string[];
  non_field_errors?: string[];
}

// Package Purchase Models
export interface PackagePurchaseRequest {
  package_id: number;
}

export interface PackagePurchaseResponse {
  detail: string;
  subscription_id: number;
  expires_at: string;
  max_selectable_games: number;
}

// My Packages Models
export interface PackageSubscription {
  id: number;
  package_name: string;
  selected_games: GamePackageGame[];
  available_games: GamePackageGame[];
  remaining_slots: number;
  expires_at: string;
}

export interface MyPackagesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: PackageSubscription[];
}

// Game Selection Models
export interface SelectGamesRequest {
  subscription_id: number;
  game_ids: number[];
}

export interface SelectGamesResponse {
  detail: string;
  remaining_slots: number;
  selected_games: GamePackageGame[];
}

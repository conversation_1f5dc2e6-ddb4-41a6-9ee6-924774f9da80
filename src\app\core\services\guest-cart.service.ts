import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { Game } from '../models/game.model';
import { GamePackage } from '../models/game-package.model';

export interface GuestCartItem {
  id: string; // UUID for guest items
  type: 'game' | 'package';
  game?: Game;
  gamePackage?: GamePackage;
  addedAt: string;
}

export interface GuestCart {
  items: GuestCartItem[];
  totalItems: number;
  totalPrice: number;
}

@Injectable({
  providedIn: 'root'
})
export class GuestCartService {
  private readonly GUEST_CART_KEY = 'guest_cart';
  private cartSubject = new BehaviorSubject<GuestCart>({ items: [], totalItems: 0, totalPrice: 0 });
  
  public cart$ = this.cartSubject.asObservable();

  constructor() {
    this.loadCartFromStorage();
  }

  private loadCartFromStorage(): void {
    try {
      const cartData = localStorage.getItem(this.GUEST_CART_KEY);
      if (cartData) {
        const cart = JSON.parse(cartData);
        this.updateCartTotals(cart);
        this.cartSubject.next(cart);
      }
    } catch (error) {
      console.error('Error loading guest cart from storage:', error);
      this.clearCart();
    }
  }

  private saveCartToStorage(cart: GuestCart): void {
    try {
      localStorage.setItem(this.GUEST_CART_KEY, JSON.stringify(cart));
    } catch (error) {
      console.error('Error saving guest cart to storage:', error);
    }
  }

  private updateCartTotals(cart: GuestCart): void {
    cart.totalItems = cart.items.length;
    cart.totalPrice = cart.items.reduce((total, item) => {
      const price = item.type === 'game' 
        ? parseFloat(item.game?.price || '0')
        : parseFloat(item.gamePackage?.price || '0');
      return total + price;
    }, 0);
  }

  private generateId(): string {
    return 'guest_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  getCart(): Observable<GuestCart> {
    return of(this.cartSubject.value);
  }

  addGame(game: Game): Observable<GuestCart> {
    const currentCart = this.cartSubject.value;
    
    // Check if game already exists
    const existingItem = currentCart.items.find(item => 
      item.type === 'game' && item.game?.id === game.id
    );

    if (existingItem) {
      throw new Error('Игра уже добавлена в корзину');
    }

    const newItem: GuestCartItem = {
      id: this.generateId(),
      type: 'game',
      game: game,
      addedAt: new Date().toISOString()
    };

    const updatedCart: GuestCart = {
      ...currentCart,
      items: [...currentCart.items, newItem]
    };

    this.updateCartTotals(updatedCart);
    this.saveCartToStorage(updatedCart);
    this.cartSubject.next(updatedCart);

    return of(updatedCart);
  }

  addPackage(gamePackage: GamePackage): Observable<GuestCart> {
    const currentCart = this.cartSubject.value;
    
    // Check if package already exists
    const existingItem = currentCart.items.find(item => 
      item.type === 'package' && item.gamePackage?.id === gamePackage.id
    );

    if (existingItem) {
      throw new Error('Пакет уже добавлен в корзину');
    }

    const newItem: GuestCartItem = {
      id: this.generateId(),
      type: 'package',
      gamePackage: gamePackage,
      addedAt: new Date().toISOString()
    };

    const updatedCart: GuestCart = {
      ...currentCart,
      items: [...currentCart.items, newItem]
    };

    this.updateCartTotals(updatedCart);
    this.saveCartToStorage(updatedCart);
    this.cartSubject.next(updatedCart);

    return of(updatedCart);
  }

  removeItem(itemId: string): Observable<GuestCart> {
    const currentCart = this.cartSubject.value;
    
    const updatedCart: GuestCart = {
      ...currentCart,
      items: currentCart.items.filter(item => item.id !== itemId)
    };

    this.updateCartTotals(updatedCart);
    this.saveCartToStorage(updatedCart);
    this.cartSubject.next(updatedCart);

    return of(updatedCart);
  }

  clearCart(): Observable<GuestCart> {
    const emptyCart: GuestCart = { items: [], totalItems: 0, totalPrice: 0 };
    
    localStorage.removeItem(this.GUEST_CART_KEY);
    this.cartSubject.next(emptyCart);

    return of(emptyCart);
  }

  isGameInCart(gameId: number): boolean {
    return this.cartSubject.value.items.some(item => 
      item.type === 'game' && item.game?.id === gameId
    );
  }

  isPackageInCart(packageId: number): boolean {
    return this.cartSubject.value.items.some(item => 
      item.type === 'package' && item.gamePackage?.id === packageId
    );
  }

  getCartCount(): number {
    return this.cartSubject.value.totalItems;
  }

  // Method to transfer guest cart to authenticated user cart
  transferToUserCart(): GuestCartItem[] {
    const items = this.cartSubject.value.items;
    this.clearCart();
    return items;
  }
}

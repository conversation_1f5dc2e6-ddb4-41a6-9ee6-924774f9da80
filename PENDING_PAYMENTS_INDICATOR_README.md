# Pending Payments Indicator Feature

This document describes the implementation of the pending payments indicator in the TOY FOR TOI application's profile sidebar.

## Overview

The pending payments indicator shows users when they have purchases that are waiting for payment. This helps users quickly identify and complete their pending transactions directly from the navigation panel.

## API Integration

### Updated User Summary Endpoint
- **GET** `/api/user/summary/`
- **Authorization**: Bearer token required
- **Response Format**:
  ```json
  {
    "cart_count": 0,
    "library_count": 1,
    "pending_payments_count": 1
  }
  ```

### New Field
- `pending_payments_count`: Number of purchases with status "pending" that require payment

## Implementation Details

### UserSummary Interface Update
Updated the interface in `src/app/core/services/user-summary.service.ts`:

```typescript
export interface UserSummary {
  cart_count: number;
  library_count: number;
  pending_payments_count: number; // New field
}
```

### Profile Sidebar Integration
Updated `src/app/features/profile/profile.html` to show the pending payments badge:

```html
<a
  routerLink="/profile/purchases"
  routerLinkActive="bg-slate-700/70"
  class="w-full text-left flex items-center justify-between text-gray-300 hover:text-white transition-colors py-1 lg:py-1.5 px-2 lg:px-2.5 rounded text-xs lg:text-sm hover:bg-slate-700/50 cursor-pointer"
>
  <span>История покупок</span>
  <span
    *ngIf="userSummary && userSummary.pending_payments_count && userSummary.pending_payments_count > 0"
    class="bg-orange-500/80 text-white text-xs px-1.5 lg:px-2 py-0.5 rounded-full min-w-[16px] lg:min-w-[20px] text-center font-medium"
    title="Ожидающие оплаты покупки"
  >
    {{ userSummary.pending_payments_count }}
  </span>
</a>
```

## UI/UX Features

### Visual Design
- **Color**: Orange badge (`bg-orange-500/80`) to indicate urgency
- **Positioning**: Right-aligned next to "История покупок" text
- **Tooltip**: Shows "Ожидающие оплаты покупки" on hover
- **Responsive**: Adapts to mobile and desktop layouts
- **Font Weight**: Medium weight for better visibility

### Display Logic
- Badge only appears when `pending_payments_count > 0`
- Automatically hides when no pending payments exist
- Real-time updates when payments are completed

### Color Scheme
- **Blue**: Library count (informational)
- **Red**: Cart count (action needed)
- **Orange**: Pending payments (urgent action needed)

## User Experience Flow

1. **User has pending purchases**: Orange badge appears next to "История покупок"
2. **User clicks on "История покупок"**: Navigates to purchase history page
3. **User completes payment**: Badge disappears automatically
4. **Real-time updates**: No page refresh needed

## Integration Points

### Automatic Updates
The pending payments count updates automatically when:
- User completes a payment
- User creates a new purchase
- User navigates between pages (summary refreshes)

### Service Integration
Uses the existing `UserSummaryService` infrastructure:
- Real-time updates via BehaviorSubject
- Automatic refresh on relevant actions
- Error handling and fallback states

## Technical Implementation

### Mock Data Update
Updated mock data for testing:
```typescript
private getMockSummary(): Observable<UserSummary> {
  const mockSummary: UserSummary = {
    cart_count: 3,
    library_count: 12,
    pending_payments_count: 1 // Added for testing
  };
  return of(mockSummary).pipe(delay(500));
}
```

### Responsive Design
- Mobile: Smaller padding and font sizes
- Desktop: Standard sizing with proper spacing
- Consistent with existing badge styling

## Benefits

### User Benefits
- **Quick Identification**: Immediately see pending payments
- **Convenient Access**: Direct link to payment page
- **Visual Clarity**: Orange color indicates urgency
- **No Manual Checking**: Automatic updates eliminate guesswork

### Business Benefits
- **Increased Conversion**: Users more likely to complete payments
- **Reduced Abandonment**: Clear visibility of pending transactions
- **Better UX**: Seamless integration with existing navigation

## File Changes

### Modified Files
- `src/app/core/services/user-summary.service.ts` - Added pending_payments_count field
- `src/app/features/profile/profile.html` - Added pending payments badge
- `USER_SUMMARY_API_README.md` - Updated documentation

### No Breaking Changes
- Backward compatible with existing API
- Graceful handling when field is missing
- Existing functionality remains unchanged

## Future Enhancements

1. **Click Actions**: Direct payment from badge click
2. **Detailed Tooltips**: Show payment amounts in tooltip
3. **Animations**: Subtle pulse animation for urgent payments
4. **Sound Notifications**: Audio alerts for new pending payments
5. **Email Integration**: Automatic reminders for pending payments

## Testing

### Manual Testing
1. Create a purchase without payment
2. Verify orange badge appears in sidebar
3. Complete payment
4. Verify badge disappears
5. Test responsive behavior on mobile/desktop

### API Testing
- Verify `/api/user/summary/` returns `pending_payments_count`
- Test with different count values (0, 1, multiple)
- Verify real-time updates work correctly

## Accessibility

- **Tooltip**: Provides context for screen readers
- **Color**: Orange provides sufficient contrast
- **Size**: Badge is large enough for touch targets
- **Semantic**: Uses proper ARIA attributes where needed

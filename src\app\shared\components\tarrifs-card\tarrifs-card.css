/* Tariff Card Specific Styles */
.tariff-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tariff-card:hover {
  transform: translateY(-10px) scale(1.03);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
}

.tariff-card::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 2px;
  background: linear-gradient(135deg, #22aaa8, #1e9896, #16807e);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tariff-card:hover::before {
  opacity: 0.8;
}

/* Plan title styling */
.plan-title {
  background: linear-gradient(135deg, #40a3ff, #22aaa8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Feature list styling */
.feature-item {
  transition: transform 0.2s ease;
}

.feature-item:hover {
  transform: translateX(5px);
}

/* Price styling */
.price-highlight {
  background: linear-gradient(135deg, #22aaa8, #40a3ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* CTA Button styling */
.cta-button {
  background: linear-gradient(135deg, #22aaa8, #1e9896);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.cta-button:hover::before {
  left: 100%;
}

.cta-button:hover {
  background: linear-gradient(135deg, #1e9896, #16807e);
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(34, 170, 168, 0.4);
}

/* Popular plan indicator */
.popular-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

/* Active subscription styling */
.active-subscription {
  border: 2px solid #10b981;
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.active-subscription::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 1rem;
  padding: 2px;
  background: linear-gradient(135deg, #10b981, #059669, #047857);
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.active-subscription:hover::before {
  opacity: 0.8;
}

/* Active subscription glow effect */
.active-subscription:hover {
  box-shadow: 0 0 30px rgba(16, 185, 129, 0.5);
}
/* Guest Cart Styles */

/* Line clamp utilities for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hover effects for images */
img.hover\:scale-105:hover {
  transform: scale(1.05);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Cart-specific enhancements */
.cart-item {
  transition: all 0.3s ease;
}

.cart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Sticky summary positioning */
.sticky {
  position: sticky;
}

/* Enhanced remove button */
.remove-button {
  transition: all 0.2s ease;
}

.remove-button:hover {
  transform: scale(1.1);
}

/* Price highlight */
.price-highlight {
  background: linear-gradient(135deg, #10b981, #059669);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Form styling enhancements */
.form-input {
  transition: all 0.2s ease;
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Registration notice styling */
.registration-notice {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.1));
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Processing overlay */
.processing-overlay {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Processing animation */
@keyframes processingPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.processing-text {
  animation: processingPulse 2s ease-in-out infinite;
}

/* Button enhancements */
.btn-primary {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, #10b981, #059669);
  transition: all 0.2s ease;
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

/* Mobile cart layout adjustments */
@media (max-width: 1023px) {
  .cart-grid {
    grid-template-columns: 1fr;
  }
  
  .cart-summary {
    position: static;
    margin-top: 1.5rem;
  }
  
  .cart-item-image {
    width: 120px !important;
    height: 90px !important;
  }
  
  .cart-item-content {
    flex-direction: column;
    gap: 1rem;
  }
}

/* Tablet adjustments */
@media (max-width: 768px) {
  .cart-item-image {
    width: 100px !important;
    height: 75px !important;
  }
  
  /* Compact form on mobile */
  .form-input {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .btn-primary,
  .btn-success {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

/* Small mobile adjustments */
@media (max-width: 480px) {
  .cart-item-image {
    width: 80px !important;
    height: 60px !important;
  }
  
  .cart-item {
    padding: 1rem !important;
  }
  
  /* Stack form elements more compactly */
  .space-y-4 > * + * {
    margin-top: 0.75rem;
  }
  
  /* Smaller text on very small screens */
  .text-2xl {
    font-size: 1.5rem;
  }
  
  .text-xl {
    font-size: 1.25rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover\:border-slate-500\/60:hover {
    border-color: inherit;
  }

  .hover\:bg-blue-700:hover {
    background-color: inherit;
  }

  .hover\:bg-green-700:hover {
    background-color: inherit;
  }

  .hover\:scale-105:hover {
    transform: none;
  }
  
  .cart-item:hover {
    transform: none;
    box-shadow: none;
  }
  
  .remove-button:hover {
    transform: none;
  }
  
  .btn-primary:hover,
  .btn-success:hover {
    transform: none;
    box-shadow: none;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .cart-item,
  .remove-button,
  .form-input,
  .btn-primary,
  .btn-success {
    transition: none;
  }
  
  .cart-item:hover {
    transform: none;
  }
  
  .remove-button:hover {
    transform: none;
  }
  
  .btn-primary:hover,
  .btn-success:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .cart-item {
    border-width: 2px;
  }
  
  .form-input {
    border-width: 2px;
  }
  
  .btn-primary,
  .btn-success {
    border: 2px solid currentColor;
  }
}

import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Header } from "./components/header/header";
import { Card } from './components/card/card';
import { TarrifsCard } from './components/tarrifs-card/tarrifs-card';
import { LoadingSpinner } from './components/loading-spinner/loading-spinner';
import { GlobalLoading } from './components/global-loading/global-loading';
import { ModalComponent } from './components/modal/modal.component';

@NgModule({
    declarations: [
        Header,
        Card,
        TarrifsCard,
        LoadingSpinner,
        GlobalLoading,
        ModalComponent
    ],
    imports: [
        CommonModule
    ],
  exports: [
    Header,
    Card,
    TarrifsCard,
    LoadingSpinner,
    GlobalLoading,
    ModalComponent
  ]
})
export class SharedModule { }

import { Component, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { GameKeyService } from '../../../../core/services/game-key.service';
import { GameService } from '../../../../core/services/game.service';
import { ModalService } from '../../../../core/services/modal.service';
import { 
  GameKey, 
  CreateGameKeyRequest, 
  GameKeyFilters, 
  GameKeyError 
} from '../../../../core/models/game-key.model';
import { Game } from '../../../../core/models/game.model';

@Component({
  selector: 'app-game-keys-management',
  standalone: false,
  templateUrl: './game-keys-management.component.html',
  styleUrl: './game-keys-management.component.css'
})
export class GameKeysManagementComponent implements OnInit {
  gameKeys: GameKey[] = [];
  games: Game[] = [];
  gameKeysLoading = false;
  gameKeysError = '';

  // Pagination
  totalGameKeys = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Search and filtering
  searchTerm = '';
  sortBy = '-id';
  selectedGame: number | null = null;
  selectedUsageStatus: 'all' | 'used' | 'unused' = 'all';
  private searchSubject = new Subject<string>();

  // Add game key form
  showAddForm = false;
  addGameKeyLoading = false;
  addGameKeyError = '';
  newGameKey: CreateGameKeyRequest = {
    game: 0,
    code: ''
  };

  constructor(
    private gameKeyService: GameKeyService,
    private gameService: GameService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadGameKeys();
    this.loadGames();
    this.setupSearch();
  }

  private setupSearch(): void {
    this.searchSubject.pipe(
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe(() => {
      this.currentPage = 1;
      this.loadGameKeys();
    });
  }

  loadGameKeys(): void {
    this.gameKeysLoading = true;
    this.gameKeysError = '';

    const filters = this.buildFilters();

    this.gameKeyService.getGameKeys(filters, this.currentPage, this.pageSize).subscribe({
      next: (response) => {
        this.gameKeys = response.results;
        this.totalGameKeys = response.count;
        this.hasNext = !!response.next;
        this.hasPrevious = !!response.previous;
        this.gameKeysLoading = false;
      },
      error: (error) => {
        this.gameKeysError = error.message || 'Failed to load game keys';
        this.gameKeysLoading = false;
      }
    });
  }

  loadGames(): void {
    this.gameService.getGames().subscribe({
      next: (response) => {
        this.games = response.results;
      },
      error: (error) => {
        console.error('Failed to load games:', error);
      }
    });
  }

  private buildFilters(): GameKeyFilters {
    const filters: GameKeyFilters = {
      ordering: this.sortBy
    };

    if (this.searchTerm.trim()) {
      filters.search = this.searchTerm.trim();
    }

    if (this.selectedGame) {
      filters.game = this.selectedGame;
    }

    if (this.selectedUsageStatus !== 'all') {
      filters.is_used = this.selectedUsageStatus === 'used';
    }

    return filters;
  }

  // Search and filter methods
  onSearchChange(): void {
    this.searchSubject.next(this.searchTerm);
  }

  onGameFilterChange(): void {
    this.currentPage = 1;
    this.loadGameKeys();
  }

  onUsageStatusFilterChange(): void {
    this.currentPage = 1;
    this.loadGameKeys();
  }

  onSortChange(): void {
    this.currentPage = 1;
    this.loadGameKeys();
  }

  // Pagination methods
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadGameKeys();
  }

  get totalPages(): number {
    return Math.ceil(this.totalGameKeys / this.pageSize);
  }

  get pages(): number[] {
    const pages = [];
    const maxPages = Math.min(this.totalPages, 10);
    const startPage = Math.max(1, this.currentPage - 5);
    const endPage = Math.min(this.totalPages, startPage + maxPages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  }

  // Add game key methods
  toggleAddForm(): void {
    this.showAddForm = !this.showAddForm;
    if (this.showAddForm) {
      this.resetAddForm();
    }
  }

  private resetAddForm(): void {
    this.newGameKey = {
      game: 0,
      code: ''
    };
    this.addGameKeyError = '';
  }

  onAddGameKey(): void {
    if (!this.newGameKey.game || !this.newGameKey.code.trim()) {
      this.addGameKeyError = 'Пожалуйста, заполните все обязательные поля';
      return;
    }

    this.addGameKeyLoading = true;
    this.addGameKeyError = '';

    this.gameKeyService.createGameKey(this.newGameKey).subscribe({
      next: (gameKey) => {
        this.gameKeys.unshift(gameKey);
        this.totalGameKeys++;
        this.showAddForm = false;
        this.addGameKeyLoading = false;
        this.modalService.success('Успех', 'Ключ игры успешно добавлен');
      },
      error: (error) => {
        this.addGameKeyLoading = false;
        if (error && typeof error === 'object') {
          const gameKeyError = error as GameKeyError;
          if (gameKeyError.game) {
            this.addGameKeyError = 'Игра: ' + gameKeyError.game.join(', ');
          } else if (gameKeyError.code) {
            this.addGameKeyError = 'Код: ' + gameKeyError.code.join(', ');
          } else if (gameKeyError.non_field_errors) {
            this.addGameKeyError = gameKeyError.non_field_errors.join(', ');
          } else {
            this.addGameKeyError = 'Произошла ошибка при добавлении ключа';
          }
        } else {
          this.addGameKeyError = error.message || 'Произошла ошибка при добавлении ключа';
        }
      }
    });
  }

  // Delete game key method
  deleteGameKey(gameKey: GameKey): void {
    this.modalService.confirm(
      'Удаление ключа игры',
      `Вы уверены, что хотите удалить ключ "${gameKey.code}" для игры "${gameKey.game_title}"?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.gameKeyService.deleteGameKey(gameKey.id).subscribe({
          next: () => {
            this.gameKeys = this.gameKeys.filter(gk => gk.id !== gameKey.id);
            this.totalGameKeys--;
            this.modalService.success('Успех', 'Ключ игры успешно удален');
          },
          error: (error) => {
            this.modalService.error('Ошибка', 'Не удалось удалить ключ игры: ' + error.message);
          }
        });
      }
    });
  }

  // Utility methods
  formatDate(dateString: string | null): string {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getGameTitle(gameId: number): string {
    const game = this.games.find(g => g.id === gameId);
    return game ? game.title : `Игра #${gameId}`;
  }

  getUsageStatusText(isUsed: boolean): string {
    return isUsed ? 'Использован' : 'Не использован';
  }

  getUsageStatusClass(isUsed: boolean): string {
    return isUsed ? 'text-red-400' : 'text-green-400';
  }
}

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { 
  GameKey, 
  GameKeyListResponse, 
  CreateGameKeyRequest, 
  GameKeyFilters 
} from '../models/game-key.model';

@Injectable({
  providedIn: 'root'
})
export class GameKeyService {
  private apiUrl = `${environment.apiUrl}/api/game-keys`;

  constructor(private http: HttpClient) {}

  /**
   * Get all game keys with optional filtering and pagination
   * @param filters - Optional filters for search, ordering, game, and usage status
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getGameKeys(filters?: GameKeyFilters, page?: number, pageSize?: number): Observable<GameKeyListResponse> {
    let params = new HttpParams();

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (filters?.ordering) {
      params = params.set('ordering', filters.ordering);
    }

    if (filters?.game) {
      params = params.set('game', filters.game.toString());
    }

    if (filters?.is_used !== undefined) {
      params = params.set('is_used', filters.is_used.toString());
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<GameKeyListResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Get a single game key by ID
   * @param id - Game key ID
   */
  getGameKey(id: number): Observable<GameKey> {
    return this.http.get<GameKey>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Create a new game key (staff only)
   * @param gameKeyData - Game key data to create
   */
  createGameKey(gameKeyData: CreateGameKeyRequest): Observable<GameKey> {
    return this.http.post<GameKey>(`${this.apiUrl}/`, gameKeyData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Update a game key (staff only)
   * @param id - Game key ID
   * @param gameKeyData - Updated game key data
   */
  updateGameKey(id: number, gameKeyData: Partial<CreateGameKeyRequest>): Observable<GameKey> {
    return this.http.patch<GameKey>(`${this.apiUrl}/${id}/`, gameKeyData).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Delete a game key (staff only)
   * @param id - Game key ID
   */
  deleteGameKey(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}/`).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    
    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 400 && error.error) {
        // Validation errors
        return throwError(() => error.error);
      } else if (error.status === 401) {
        errorMessage = 'Unauthorized access';
      } else if (error.status === 403) {
        errorMessage = 'Access forbidden';
      } else if (error.status === 404) {
        errorMessage = 'Resource not found';
      } else if (error.status === 500) {
        errorMessage = 'Internal server error';
      } else {
        errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }
}

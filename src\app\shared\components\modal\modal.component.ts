import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { ModalService, ModalConfig } from '../../../core/services/modal.service';

@Component({
  selector: 'app-modal',
  standalone: false,
  templateUrl: './modal.component.html',
  styleUrl: './modal.component.css'
})
export class ModalComponent implements OnInit, OnDestroy {
  modalConfig: ModalConfig | null = null;
  isVisible = false;
  private subscription?: Subscription;

  constructor(private modalService: ModalService) {}

  ngOnInit(): void {
    this.subscription = this.modalService.modal$.subscribe(config => {
      this.modalConfig = config;
      this.isVisible = config !== null;
    });
  }

  ngOnDestroy(): void {
    this.subscription?.unsubscribe();
  }

  onConfirm(): void {
    if (this.modalConfig?.type === 'confirm') {
      this.modalService.confirmModal();
    } else {
      this.modalService.closeModal();
    }
  }

  onCancel(): void {
    this.modalService.dismissModal();
  }

  onBackdropClick(): void {
    // Only allow backdrop dismiss for non-critical modals
    if (this.modalConfig?.type !== 'error') {
      this.onCancel();
    }
  }

  getIconClass(): string {
    switch (this.modalConfig?.type) {
      case 'confirm':
        return 'text-yellow-400';
      case 'error':
        return 'text-red-400';
      case 'success':
        return 'text-green-400';
      default:
        return 'text-blue-400';
    }
  }

  getIcon(): string {
    switch (this.modalConfig?.type) {
      case 'confirm':
        return 'M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z';
      case 'error':
        return 'M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z';
      case 'success':
        return 'M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
      default:
        return 'M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z';
    }
  }

  getButtonClass(type: 'confirm' | 'cancel'): string {
    if (type === 'cancel') {
      return 'px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors';
    }

    switch (this.modalConfig?.type) {
      case 'confirm':
        return 'px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors';
      case 'error':
        return 'px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors';
      case 'success':
        return 'px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors';
      default:
        return 'px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors';
    }
  }
}

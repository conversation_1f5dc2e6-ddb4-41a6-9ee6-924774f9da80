export interface GameAccess {
  id: number;
  user: number;
  user_email: string;
  game: number;
  game_title: string;
  access_type: 'oneday' | 'subscription';
  access_start: string;
  access_end: string;
  has_access: boolean;
}

export interface GameAccessListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: GameAccess[];
}

export interface CreateGameAccessRequest {
  user: number;
  game: number;
  access_type: 'oneday' | 'subscription';
  access_start?: string;
  access_end?: string;
}

export interface UpdateGameAccessRequest {
  access_type?: 'oneday' | 'subscription';
  access_start?: string;
  access_end?: string;
}

export interface GameAccessFilters {
  search?: string;
  user?: number;
  game?: number;
  access_type?: 'oneday' | 'subscription';
  has_access?: boolean;
  ordering?: string;
}

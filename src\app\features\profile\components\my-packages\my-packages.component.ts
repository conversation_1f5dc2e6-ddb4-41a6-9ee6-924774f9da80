import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';
import { GamePackageService } from '../../../../core/services/game-package.service';
import { ModalService } from '../../../../core/services/modal.service';
import { 
  PackageSubscription, 
  MyPackagesResponse,
  GamePackageGame 
} from '../../../../core/models/game-package.model';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-my-packages',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './my-packages.component.html',
  styleUrls: ['./my-packages.component.css']
})

export class MyPackagesComponent implements OnInit, OnDestroy {
  environment = environment;

  packages: PackageSubscription[] = [];
  packagesLoading = false;
  packagesError = '';

  // Pagination
  totalPackages = 0;
  currentPage = 1;
  pageSize = 12;
  hasNext = false;
  hasPrevious = false;

  // Game selection
  selectedPackageId: number | null = null;
  selectedGameIds: number[] = [];
  selectingGames = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private packageService: GamePackageService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.loadMyPackages();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  /**
   * Load user's package subscriptions
   */
  loadMyPackages(): void {
    this.packagesLoading = true;
    this.packagesError = '';

    this.packageService.getMyPackages(this.currentPage, this.pageSize).subscribe({
      next: (response: MyPackagesResponse) => {
        this.packages = response.results;
        this.totalPackages = response.count;
        this.hasNext = response.next !== null;
        this.hasPrevious = response.previous !== null;
        this.packagesLoading = false;

        // Automatically set the first package with remaining slots as current
        if (this.packages.length > 0) {
          const packageWithSlots = this.packages.find(p => p.remaining_slots > 0 && !this.isPackageExpired(p.expires_at));
          if (packageWithSlots) {
            this.selectedPackageId = packageWithSlots.id;
          } else {
            // If no package has remaining slots, set the first one
            this.selectedPackageId = this.packages[0].id;
          }
        }
      },
      error: (error) => {
        console.error('Error loading my packages:', error);
        this.packagesError = error.message || 'Ошибка загрузки пакетов';
        this.packagesLoading = false;
      }
    });
  }



  /**
   * Toggle game selection
   */
  toggleGameSelection(gameId: number): void {
    const index = this.selectedGameIds.indexOf(gameId);
    if (index > -1) {
      this.selectedGameIds.splice(index, 1);
    } else {
      const currentPackage = this.packages.find(p => p.id === this.selectedPackageId);
      if (currentPackage && this.selectedGameIds.length < currentPackage.remaining_slots) {
        this.selectedGameIds.push(gameId);
      } else {
        this.modalService.alert('Лимит достигнут', 'Вы не можете выбрать больше игр из этого пакета.');
      }
    }
  }

  /**
   * Check if game is selected
   */
  isGameSelected(gameId: number): boolean {
    return this.selectedGameIds.includes(gameId);
  }

  /**
   * Confirm game selection
   */
  confirmGameSelection(): void {
    if (!this.selectedPackageId || this.selectedGameIds.length === 0) {
      this.modalService.error('Ошибка', 'Выберите хотя бы одну игру.');
      return;
    }

    this.selectingGames = true;

    this.packageService.selectGamesFromPackage(this.selectedPackageId, this.selectedGameIds).subscribe({
      next: (response) => {
        this.selectingGames = false;
        this.modalService.success('Успех!', response.detail);
        this.selectedGameIds = []; // Clear selection
        this.loadMyPackages(); // Refresh the packages list
      },
      error: (error) => {
        this.selectingGames = false;
        console.error('Error selecting games:', error);
        this.modalService.error('Ошибка', error.message || 'Ошибка при выборе игр');
      }
    });
  }

  /**
   * Get current package for game selection
   */
  getCurrentPackage(): PackageSubscription | null {
    return this.packages.find(p => p.id === this.selectedPackageId) || null;
  }

  /**
   * Check if package is expired
   */
  isPackageExpired(expiresAt: string): boolean {
    return new Date(expiresAt) < new Date();
  }

  /**
   * Calculate days remaining until subscription expires
   */
  getDaysRemaining(expiresAt: string | undefined): number {
    if (!expiresAt) return 0;

    const expiryDate = new Date(expiresAt);
    const currentDate = new Date();
    const timeDiff = expiryDate.getTime() - currentDate.getTime();
    const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

    return Math.max(0, daysDiff);
  }

  /**
   * Check if current package has available games to select
   */
  hasAvailableGames(): boolean {
    const currentPackage = this.getCurrentPackage();
    return !!(currentPackage?.available_games && currentPackage.available_games.length > 0);
  }

  /**
   * Format expiration date
   */
  formatExpirationDate(expiresAt: string): string {
    const date = new Date(expiresAt);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Pagination methods
   */
  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadMyPackages();
  }

  getTotalPages(): number {
    return Math.ceil(this.totalPackages / this.pageSize);
  }

  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }
}

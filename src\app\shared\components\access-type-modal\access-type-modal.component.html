<!-- Access Type Selection Modal -->
<div
  *ngIf="isVisible"
  class="fixed inset-0 z-[70] flex items-center justify-center bg-black/40 backdrop-blur-sm"
  (click)="closeModal()"
>
  <!-- Modal Content -->
  <div 
    class="relative bg-gradient-to-br from-slate-800 via-slate-800 to-slate-900 border border-slate-600/50 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
    (click)="$event.stopPropagation()"
  >
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-slate-600/50 p-6">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-xl font-bold text-white mb-1">Выберите тип доступа</h3>
          <p class="text-gray-300 text-sm">{{ selection?.gameTitle || selection?.packageTitle }}</p>
        </div>
        <button
          (click)="closeModal()"
          class="text-gray-400 hover:text-white transition-colors p-1"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Access Type Options -->
    <div class="p-6 space-y-4">
      <div 
        *ngFor="let accessType of accessTypes"
        (click)="selectAccessType(accessType.id)"
        class="relative cursor-pointer group"
      >
        <div 
          class="border-2 rounded-xl p-4 transition-all duration-300"
          [class]="selectedAccessType === accessType.id 
            ? 'border-blue-500 bg-blue-500/10' 
            : 'border-slate-600/50 hover:border-slate-500/70 bg-slate-700/30 hover:bg-slate-700/50'"
        >
          <!-- Selection Indicator -->
          <div class="absolute top-4 right-4">
            <div 
              class="w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all"
              [class]="selectedAccessType === accessType.id 
                ? 'border-blue-500 bg-blue-500' 
                : 'border-slate-400'"
            >
              <div 
                *ngIf="selectedAccessType === accessType.id"
                class="w-2 h-2 bg-white rounded-full"
              ></div>
            </div>
          </div>

          <!-- Content -->
          <div class="pr-8">
            <!-- Icon and Title -->
            <div class="flex items-center gap-3 mb-2">
              <!-- Clock Icon for oneday -->
              <div 
                *ngIf="accessType.icon === 'clock'"
                class="w-8 h-8 rounded-lg flex items-center justify-center"
                [class]="selectedAccessType === accessType.id 
                  ? 'bg-blue-500/20 text-blue-400' 
                  : 'bg-slate-600/50 text-slate-400'"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>

              <!-- Calendar Icon for subscription -->
              <div 
                *ngIf="accessType.icon === 'calendar'"
                class="w-8 h-8 rounded-lg flex items-center justify-center"
                [class]="selectedAccessType === accessType.id 
                  ? 'bg-blue-500/20 text-blue-400' 
                  : 'bg-slate-600/50 text-slate-400'"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>

              <div>
                <h4 
                  class="font-semibold transition-colors"
                  [class]="selectedAccessType === accessType.id ? 'text-blue-400' : 'text-white'"
                >
                  {{ accessType.name }}
                </h4>
                <p class="text-gray-400 text-sm">{{ accessType.duration }}</p>
              </div>
            </div>

            <!-- Description -->
            <p class="text-gray-300 text-sm mb-3">{{ accessType.description }}</p>

            <!-- Price -->
            <div class="flex items-center justify-between">
              <span class="text-green-400 font-bold text-lg">
                {{ formatPrice(getAccessTypePrice(accessType.id)) }}
              </span>
              <span 
                *ngIf="accessType.id === 'subscription'"
                class="text-xs px-2 py-1 rounded-full"
                [class]="selectedAccessType === accessType.id 
                  ? 'bg-blue-500/20 text-blue-400' 
                  : 'bg-slate-600/50 text-slate-400'"
              >
                Лучшее предложение
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="border-t border-slate-600/50 p-6 bg-slate-800/50">
      <div class="flex gap-3">
        <button
          (click)="closeModal()"
          class="flex-1 px-4 py-3 bg-slate-600/50 hover:bg-slate-600/70 text-gray-300 hover:text-white rounded-xl transition-all text-sm font-medium"
        >
          Отмена
        </button>
        <button
          (click)="confirmSelection()"
          [disabled]="!selectedAccessType"
          class="flex-1 px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-xl transition-all text-sm font-medium shadow-lg"
        >
          Подтвердить выбор
        </button>
      </div>
    </div>
  </div>
</div>

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, delay } from 'rxjs';
import { environment } from '../../environments/environment';
import { User, UserListResponse, UserFilters, UserUpdateRequest } from '../models/user.model';
import { SAMPLE_USERS } from '../../test-data/sample-users';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private apiUrl = `${environment.apiUrl}/api/admin/users`;
  private useSampleData = false; // Set to false when real API is available

  constructor(private http: HttpClient) {}

  /**
   * Get all users with optional filtering and pagination
   */
  getUsers(filters?: UserFilters, page?: number, pageSize?: number): Observable<UserListResponse> {
    if (this.useSampleData) {
      return this.getSampleUsers(filters, page, pageSize);
    }

    let params = new HttpParams();

    // Add pagination parameters
    if (page) {
      params = params.set('page', page.toString());
    }
    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    // Add filter parameters
    if (filters) {
      if (filters.search) {
        params = params.set('search', filters.search);
      }
      if (filters.is_staff !== undefined) {
        params = params.set('is_staff', filters.is_staff.toString());
      }
      if (filters.is_active !== undefined) {
        params = params.set('is_active', filters.is_active.toString());
      }
      if (filters.is_superuser !== undefined) {
        params = params.set('is_superuser', filters.is_superuser.toString());
      }
      if (filters.ordering) {
        params = params.set('ordering', filters.ordering);
      }
    }

    return this.http.get<UserListResponse>(`${this.apiUrl}/`, { params });
  }

  /**
   * Get sample users for testing (simulates API behavior)
   */
  private getSampleUsers(filters?: UserFilters, page: number = 1, pageSize: number = 10): Observable<UserListResponse> {
    let filteredUsers = [...SAMPLE_USERS];

    // Apply filters
    if (filters) {
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredUsers = filteredUsers.filter(user =>
          user.email.toLowerCase().includes(searchTerm) ||
          (user.phone && user.phone.toLowerCase().includes(searchTerm)) ||
          user.user_code.toLowerCase().includes(searchTerm)
        );
      }

      if (filters.is_staff !== undefined) {
        filteredUsers = filteredUsers.filter(user => user.is_staff === filters.is_staff);
      }

      if (filters.is_active !== undefined) {
        filteredUsers = filteredUsers.filter(user => user.is_active === filters.is_active);
      }

      if (filters.is_superuser !== undefined) {
        filteredUsers = filteredUsers.filter(user => user.is_superuser === filters.is_superuser);
      }

      // Apply sorting
      if (filters.ordering) {
        const isDescending = filters.ordering.startsWith('-');
        const field = isDescending ? filters.ordering.substring(1) : filters.ordering;

        filteredUsers.sort((a, b) => {
          let aValue = (a as any)[field];
          let bValue = (b as any)[field];

          if (field === 'date_joined' || field === 'last_login') {
            aValue = new Date(aValue || 0).getTime();
            bValue = new Date(bValue || 0).getTime();
          } else if (typeof aValue === 'string') {
            aValue = aValue.toLowerCase();
            bValue = bValue.toLowerCase();
          }

          if (aValue < bValue) return isDescending ? 1 : -1;
          if (aValue > bValue) return isDescending ? -1 : 1;
          return 0;
        });
      }
    }

    // Apply pagination
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    const response: UserListResponse = {
      count: filteredUsers.length,
      next: endIndex < filteredUsers.length ? `page=${page + 1}` : null,
      previous: page > 1 ? `page=${page - 1}` : null,
      results: paginatedUsers
    };

    // Simulate API delay
    return of(response).pipe(delay(500));
  }

  /**
   * Get a single user by ID
   */
  getUser(id: number): Observable<User> {
    return this.http.get<User>(`${this.apiUrl}/${id}/`);
  }

  /**
   * Update a user (staff only)
   */
  updateUser(id: number, userData: UserUpdateRequest): Observable<User> {
    if (this.useSampleData) {
      return this.updateSampleUser(id, userData);
    }
    return this.http.patch<User>(`${this.apiUrl}/${id}/`, userData);
  }

  /**
   * Delete a user (staff only)
   * Note: Superusers cannot be deleted and will return 403 Forbidden
   */
  deleteUser(id: number): Observable<void> {
    if (this.useSampleData) {
      return this.deleteSampleUser(id);
    }
    return this.http.delete<void>(`${this.apiUrl}/${id}/`);
  }

  /**
   * Activate/Deactivate a user (staff only)
   */
  toggleUserStatus(id: number, isActive: boolean): Observable<User> {
    return this.updateUser(id, { is_active: isActive });
  }

  /**
   * Toggle staff status (superuser only)
   */
  toggleStaffStatus(id: number, isStaff: boolean): Observable<User> {
    return this.updateUser(id, { is_staff: isStaff });
  }

  /**
   * Update sample user for testing
   */
  private updateSampleUser(id: number, userData: UserUpdateRequest): Observable<User> {
    const userIndex = SAMPLE_USERS.findIndex((user: User) => user.id === id);
    if (userIndex === -1) {
      throw new Error('User not found');
    }

    const updatedUser = { ...SAMPLE_USERS[userIndex], ...userData };
    SAMPLE_USERS[userIndex] = updatedUser;

    return of(updatedUser).pipe(delay(300));
  }

  /**
   * Delete sample user for testing
   */
  private deleteSampleUser(id: number): Observable<void> {
    const userIndex = SAMPLE_USERS.findIndex((user: User) => user.id === id);
    if (userIndex === -1) {
      throw new Error('User not found');
    }

    SAMPLE_USERS.splice(userIndex, 1);
    return of(void 0).pipe(delay(300));
  }
}

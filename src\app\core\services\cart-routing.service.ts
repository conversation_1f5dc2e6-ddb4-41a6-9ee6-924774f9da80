import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class CartRoutingService {

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Navigate to the unified cart page
   */
  navigateToCart(): void {
    // All users now go to the unified cart page
    this.router.navigate(['/cart']);
  }

  /**
   * Get the cart route
   */
  getCartRoute(): string {
    // All users now use the unified cart route
    return '/cart';
  }

  /**
   * Check if user is currently on the cart page
   */
  isOnCartPage(): boolean {
    const currentUrl = this.router.url;
    return currentUrl === '/cart';
  }
}

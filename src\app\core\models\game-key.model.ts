export interface GameKey {
  id: number;
  game: number;
  game_title: string;
  code: string;
  is_used: boolean;
  assigned_to_user: number | null;
  assigned_at: string | null;
  expires_at: string | null;
}

export interface GameKeyListResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: <PERSON><PERSON>ey[];
}

export interface CreateGameKeyRequest {
  game: number;
  code: string;
  expires_at?: string;
}

export interface GameKeyFilters {
  search?: string;
  ordering?: string;
  game?: number;
  is_used?: boolean;
}

export interface GameKeyError {
  game?: string[];
  code?: string[];
  expires_at?: string[];
  non_field_errors?: string[];
}

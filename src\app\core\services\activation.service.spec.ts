import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ActivationService, ActivateGameRequest, ActivateGameResponse } from './activation.service';
import { environment } from '../../environments/environment';

describe('ActivationService', () => {
  let service: ActivationService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/api/access/activate`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [ActivationService]
    });
    service = TestBed.inject(ActivationService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('activateGame', () => {
    it('should activate a game with provided parameters', () => {
      const gameId = 5;
      const accessStart = '2025-07-17T14:51:51.582Z';
      const mockResponse: ActivateGameResponse = {
        detail: 'Game access activated successfully',
        access_start: accessStart,
        access_end: '2025-07-18T14:51:51.582Z'
      };

      service.activateGame(gameId, accessStart).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({
        game_id: gameId,
        access_start: accessStart
      });
      req.flush(mockResponse);
    });

    it('should handle 400 error with field-specific errors', () => {
      const gameId = 5;
      const accessStart = 'invalid-date';

      service.activateGame(gameId, accessStart).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toContain('Access start error');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(
        { access_start: ['Invalid date format.'] },
        { status: 400, statusText: 'Bad Request' }
      );
    });

    it('should handle 404 error for unactivated access not found', () => {
      const gameId = 999;
      const accessStart = '2025-07-17T14:51:51.582Z';

      service.activateGame(gameId, accessStart).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error.message).toBe('Unactivated access not found.');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(
        { detail: 'Unactivated access not found.' },
        { status: 404, statusText: 'Not Found' }
      );
    });
  });

  describe('activateGameNow', () => {
    it('should activate a game with current timestamp', () => {
      const gameId = 5;
      const mockResponse: ActivateGameResponse = {
        detail: 'Game access activated successfully',
        access_start: '2025-07-17T14:51:51.582Z',
        access_end: '2025-07-18T14:51:51.582Z'
      };

      service.activateGameNow(gameId).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.game_id).toBe(gameId);
      expect(req.request.body.access_start).toBeDefined();
      // Verify the access_start is a valid ISO string
      expect(new Date(req.request.body.access_start).toISOString()).toBe(req.request.body.access_start);
      req.flush(mockResponse);
    });
  });
});

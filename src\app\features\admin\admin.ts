import { Component, OnInit, On<PERSON><PERSON>roy, Renderer2, HostListener } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { UserSummaryService, UserSummary } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-admin',
  standalone: false,
  templateUrl: './admin.html',
  styleUrl: './admin.css'
})
export class Admin implements OnInit, OnDestroy {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // User summary data
  userSummary: UserSummary | null = null;
  private summarySubscription?: Subscription;
  private routerSubscription?: Subscription;

  // Mobile sidebar state
  isMobileSidebarOpen = false;

  // Current active route
  activeRoute: string = 'users';
  isChildRoute: boolean = false;

  constructor(
    private authService: AuthService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router,
    private route: ActivatedRoute,
    private renderer: Renderer2
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadUserSummary();
    this.setupRouterSubscription();
    this.updateActiveRouteFromUrl();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.summarySubscription) {
      this.summarySubscription.unsubscribe();
    }
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    // Ensure body scroll is restored
    this.renderer.removeClass(document.body, 'mobile-sidebar-open');
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
        
        // Check if user has admin access
        if (!this.hasAdminAccess()) {
          this.router.navigate(['/profile']);
        }
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  loadUserSummary(): void {
    // Subscribe to the summary observable for real-time updates
    this.summarySubscription = this.userSummaryService.summary$.subscribe(summary => {
      this.userSummary = summary;
    });

    // Load initial summary data
    this.userSummaryService.getUserSummary().subscribe({
      next: (summary) => {
        // Summary is automatically updated via the subscription above
      },
      error: (error) => {
        console.error('Failed to load user summary:', error);
        // Don't show error to user as this is supplementary data
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
    this.refreshUserSummary();
  }

  refreshUserSummary(): void {
    this.userSummaryService.refreshSummary();
  }

  // Setup router subscription to close sidebar on navigation and update active route
  setupRouterSubscription(): void {
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.closeMobileSidebar();
        this.updateActiveRouteFromUrl();
      });
  }

  // Update active route based on current URL
  updateActiveRouteFromUrl(): void {
    const url = this.router.url;

    // Check if we're on a child route
    if (url.includes('/admin/users/')) {
      this.isChildRoute = true;
      this.activeRoute = 'users';
    } else {
      this.isChildRoute = false;

      // Determine active route from URL
      if (url.includes('/admin/users')) {
        this.activeRoute = 'users';
      } else if (url.includes('/admin/admin-games')) {
        this.activeRoute = 'admin-games';
      } else if (url.includes('/admin/admin-library')) {
        this.activeRoute = 'admin-library';
      } else if (url.includes('/admin/game-files')) {
        this.activeRoute = 'game-files';
      } else if (url.includes('/admin/game-access')) {
        this.activeRoute = 'game-access';
      } else if (url.includes('/admin/admin-packages')) {
        this.activeRoute = 'admin-packages';
      } else {
        this.activeRoute = 'users'; // Default
      }
    }
  }

  // Mobile sidebar methods
  toggleMobileSidebar(): void {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
    this.updateBodyScroll();
  }

  closeMobileSidebar(): void {
    this.isMobileSidebarOpen = false;
    this.updateBodyScroll();
  }

  // Update body scroll based on sidebar state
  private updateBodyScroll(): void {
    if (this.isMobileSidebarOpen) {
      this.renderer.addClass(document.body, 'mobile-sidebar-open');
    } else {
      this.renderer.removeClass(document.body, 'mobile-sidebar-open');
    }
  }

  // Handle escape key to close mobile sidebar
  @HostListener('document:keydown.escape')
  onEscapeKey(): void {
    if (this.isMobileSidebarOpen) {
      this.closeMobileSidebar();
    }
  }

  // Check if user has admin access
  hasAdminAccess(): boolean {
    return this.userProfile?.is_staff || false;
  }
}

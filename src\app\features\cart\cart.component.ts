import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { CartService } from '../../core/services/cart.service';
import { CheckoutService } from '../../core/services/checkout.service';
import { UserSummaryService } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { AuthService } from '../../core/services/auth.service';
import { GuestCartService, GuestCart, GuestCartItem } from '../../core/services/guest-cart.service';
import { EnhancedRegistrationService } from '../../core/services/enhanced-registration.service';
import { Cart, CartItem, Purchase } from '../../core/models/cart.model';

@Component({
  selector: 'app-cart',
  standalone: false,
  templateUrl: './cart.component.html',
  styleUrl: './cart.component.css'
})
export class CartComponent implements OnInit, OnDestroy {
  cart: Cart = { items: [], total_items: 0, total_price: 0 };
  guestCart: GuestCart = { items: [], totalItems: 0, totalPrice: 0 };
  loading = false;
  error = '';
  checkoutLoading = false;

  // Registration form (for guest users)
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  loginForm: FormGroup;
  showCodeField = false;
  showLoginForm = false;
  registeredEmail = '';
  registrationPassword = '';
  registrationLoading = false;
  verificationLoading = false;
  loginLoading = false;
  registrationError = '';
  fieldErrors: any = {};
  isProcessingRegistration = false;

  private cartSubscription?: Subscription;
  private guestCartSubscription?: Subscription;

  constructor(
    private cartService: CartService,
    private checkoutService: CheckoutService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private authService: AuthService,
    private guestCartService: GuestCartService,
    private enhancedRegistrationService: EnhancedRegistrationService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      password_confirm: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });

    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]]
    });
  }

  ngOnInit(): void {
    this.loadCart();
    this.setupCartSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
    this.guestCartSubscription?.unsubscribe();
  }

  isAuthenticated(): boolean {
    return this.authService.isAuthenticated();
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('password_confirm');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      return { passwordMismatch: true };
    }
    return null;
  }

  private setupCartSubscription(): void {
    if (this.authService.isAuthenticated()) {
      this.cartSubscription = this.cartService.cart$.subscribe(cart => {
        this.cart = cart;
      });
    } else {
      this.guestCartSubscription = this.guestCartService.cart$.subscribe(cart => {
        this.guestCart = cart;
      });
    }
  }

  loadCart(): void {
    this.loading = true;
    this.error = '';

    if (this.authService.isAuthenticated()) {
      this.cartService.loadCart().subscribe({
        next: (cart) => {
          this.cart = cart;
          this.loading = false;
        },
        error: (error) => {
          this.error = error.message || 'Не удалось загрузить корзину';
          this.loading = false;
        }
      });
    } else {
      // For guest users, cart is already loaded from localStorage via subscription
      this.loading = false;
    }
  }

  // Unified methods to work with both authenticated and guest carts
  getTotalItems(): number {
    return this.authService.isAuthenticated() ? this.cart.total_items : this.guestCart.totalItems;
  }

  getTotalPrice(): string | number {
    return this.authService.isAuthenticated() ? this.cart.total_price : this.guestCart.totalPrice;
  }

  getCartItems(): (CartItem | GuestCartItem)[] {
    return this.authService.isAuthenticated() ? this.cart.items : this.guestCart.items;
  }

  // Helper methods to get item data - work with both CartItem and GuestCartItem
  getItemTitle(item: CartItem | GuestCartItem): string {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.title;
      } else if (cartItem.game_package_obj) {
        return cartItem.game_package_obj.name;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.title;
      } else if (guestItem.gamePackage) {
        return guestItem.gamePackage.name;
      }
    }
    return 'Unknown Item';
  }

  getItemPrice(item: CartItem | GuestCartItem): string {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.price;
      } else if (cartItem.game_package_obj) {
        return cartItem.game_package_obj.price;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.price;
      } else if (guestItem.gamePackage) {
        return guestItem.gamePackage.price;
      }
    }
    return '0';
  }

  getItemImage(item: CartItem | GuestCartItem): string | null {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.cover_image;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.cover_image;
      }
    }
    // Game packages don't have cover images
    return null;
  }

  getItemDescription(item: CartItem | GuestCartItem): string {
    if (this.isCartItem(item)) {
      const cartItem = item as CartItem;
      if (cartItem.game_obj) {
        return cartItem.game_obj.description;
      } else if (cartItem.game_package_obj) {
        return cartItem.game_package_obj.description;
      }
    } else {
      const guestItem = item as GuestCartItem;
      if (guestItem.game) {
        return guestItem.game.description;
      } else if (guestItem.gamePackage) {
        return guestItem.gamePackage.description;
      }
    }
    return '';
  }

  isGameItem(item: CartItem | GuestCartItem): boolean {
    if (this.isCartItem(item)) {
      return !!(item as CartItem).game_obj;
    } else {
      return !!(item as GuestCartItem).game;
    }
  }

  isPackageItem(item: CartItem | GuestCartItem): boolean {
    if (this.isCartItem(item)) {
      return !!(item as CartItem).game_package_obj;
    } else {
      return !!(item as GuestCartItem).gamePackage;
    }
  }

  // Type guard to distinguish between CartItem and GuestCartItem
  private isCartItem(item: CartItem | GuestCartItem): item is CartItem {
    return this.authService.isAuthenticated();
  }

  // Additional helper methods for the template
  getGameId(item: CartItem | GuestCartItem): number | undefined {
    if (this.isCartItem(item)) {
      return (item as CartItem).game_obj?.id;
    } else {
      return (item as GuestCartItem).game?.id;
    }
  }

  getPackageGames(item: CartItem | GuestCartItem): any[] | undefined {
    if (this.isCartItem(item)) {
      return (item as CartItem).game_package_obj?.games;
    } else {
      return (item as GuestCartItem).gamePackage?.games;
    }
  }

  getPackageDuration(item: CartItem | GuestCartItem): number | undefined {
    if (this.isCartItem(item)) {
      return (item as CartItem).game_package_obj?.duration_days;
    } else {
      return (item as GuestCartItem).gamePackage?.duration_days;
    }
  }

  getItemType(item: CartItem | GuestCartItem): string {
    return this.isGameItem(item) ? 'Игра' : 'Пакет';
  }



  removeItem(item: CartItem | GuestCartItem): void {
    const itemTitle = this.getItemTitle(item);
    const itemType = this.isGameItem(item) ? 'игру' : 'пакет';
    this.modalService.confirm(
      'Удаление из корзины',
      `Вы уверены, что хотите удалить "${itemTitle}" из корзины?`,
      'Удалить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        if (this.authService.isAuthenticated()) {
          const cartItem = item as CartItem;
          this.cartService.removeFromCart(cartItem.id).subscribe({
            next: () => {
              console.log('Item removed successfully');
            },
            error: (error) => {
              console.error('Error removing item:', error.message);
              this.modalService.error('Ошибка', `Не удалось удалить ${itemType} из корзины: ` + error.message);
            }
          });
        } else {
          const guestItem = item as GuestCartItem;
          this.guestCartService.removeItem(guestItem.id).subscribe({
            next: () => {
              console.log('Guest item removed successfully');
            },
            error: (error) => {
              console.error('Error removing guest item:', error.message);
              this.modalService.error('Ошибка', `Не удалось удалить ${itemType} из корзины: ` + error.message);
            }
          });
        }
      }
    });
  }

  clearCart(): void {
    this.modalService.confirm(
      'Очистка корзины',
      'Вы уверены, что хотите удалить все игры из корзины?',
      'Очистить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        if (this.authService.isAuthenticated()) {
          this.cartService.clearCart().subscribe({
            next: () => {
              console.log('Cart cleared successfully');
            },
            error: (error) => {
              console.error('Error clearing cart:', error.message);
              this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
            }
          });
        } else {
          this.guestCartService.clearCart().subscribe({
            next: () => {
              console.log('Guest cart cleared successfully');
            },
            error: (error) => {
              console.error('Error clearing guest cart:', error.message);
              this.modalService.error('Ошибка', 'Не удалось очистить корзину: ' + error.message);
            }
          });
        }
      }
    });
  }



  checkout(): void {
    if (this.getCartItems().length === 0) {
      this.modalService.error('Ошибка', 'Корзина пуста');
      return;
    }

    if (!this.authService.isAuthenticated()) {
      this.modalService.error('Требуется авторизация', 'Для оформления заказа необходимо войти в систему или зарегистрироваться.');
      this.router.navigate(['/login']);
      return;
    }

    const totalItems = this.getTotalItems();
    this.modalService.confirm(
      'Подтверждение покупки',
      `Вы уверены, что хотите купить ${totalItems} ${totalItems === 1 ? 'игру' : totalItems < 5 ? 'игры' : 'игр'} на сумму ${this.formatPrice(this.getTotalPrice())}?`,
      'Купить',
      'Отмена'
    ).then(confirmed => {
      if (confirmed) {
        this.processCheckout();
      }
    });
  }

  private processCheckout(): void {
    this.checkoutLoading = true;

    this.checkoutService.checkout().subscribe({
      next: (purchases: Purchase[]) => {
        this.checkoutLoading = false;

        // Refresh user summary to update cart count (cart should be cleared after checkout)
        this.userSummaryService.refreshSummary();

        // Show success message
        const purchaseCount = purchases.length;
        const successMessage = `Успешно приобретено ${purchaseCount} ${purchaseCount === 1 ? 'игра' : purchaseCount < 5 ? 'игры' : 'игр'}!`;

        this.modalService.success('Покупки созданы', successMessage + ' Сейчас вы будете перенаправлены на страницу оплаты.').then(() => {
          // Navigate directly to purchase history for payment
          this.router.navigate(['/profile/purchases']);
        });
      },
      error: (error) => {
        this.checkoutLoading = false;
        console.error('Checkout error:', error.message);
        this.modalService.error('Ошибка покупки', 'Не удалось завершить покупку: ' + error.message);
      }
    });
  }

  formatPrice(price: string | number): string {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  goToGames(): void {
    // Navigate to main page games section instead of profile games catalog
    this.router.navigate(['/']).then(() => {
      setTimeout(() => {
        const element = document.getElementById('games');
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.offsetTop - headerHeight;
          window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
          });
        }
      }, 100);
    });
  }

  viewGameDetails(gameId: number | undefined): void {
    if (gameId) {
      this.router.navigate(['/games', gameId]);
    }
  }

  // Registration methods (for guest users)
  onLogin(): void {
    if (this.loginForm.valid) {
      this.registrationError = '';
      this.clearFieldErrors();
      this.loginLoading = true;
      this.isProcessingRegistration = true;

      const credentials = this.loginForm.value;

      this.enhancedRegistrationService.loginAndTransferCart(
        credentials.email,
        credentials.password
      ).subscribe({
        next: (result) => {
          this.loginLoading = false;
          this.isProcessingRegistration = false;

          let message = result.message;
          if (result.cartTransferred && result.transferredItemsCount > 0) {
            message += ` Перенесено товаров: ${result.transferredItemsCount}.`;
          }

          this.modalService.success('Добро пожаловать!', message).then(() => {
            // Reload the page to show authenticated cart
            window.location.reload();
          });
        },
        error: (error) => {
          this.loginLoading = false;
          this.isProcessingRegistration = false;
          this.handleRegistrationError(error);
        }
      });
    }
  }

  onRegister(): void {
    if (this.registrationForm.valid) {
      this.registrationError = '';
      this.clearFieldErrors();
      this.registrationLoading = true;

      const credentials = this.registrationForm.value;
      this.registrationPassword = credentials.password; // Store for auto-login

      this.authService.register(credentials).subscribe({
        next: (response) => {
          this.registeredEmail = response.email;
          this.showCodeField = true;
          this.registrationLoading = false;
          this.modalService.success('Регистрация', 'Код подтверждения отправлен на ваш email');
        },
        error: (error) => {
          this.registrationLoading = false;
          this.handleRegistrationError(error);
        }
      });
    }
  }

  onVerifyCode(): void {
    if (this.verificationForm.valid) {
      this.registrationError = '';
      this.isProcessingRegistration = true;
      this.verificationLoading = true;

      const verificationData = {
        email: this.registeredEmail,
        code: this.verificationForm.value.code
      };

      // First verify the code using the regular auth service
      this.authService.verifyCode(verificationData).subscribe({
        next: () => {
          // After successful verification, automatically log in and transfer cart
          this.enhancedRegistrationService.loginAndTransferCart(
            this.registeredEmail,
            this.registrationPassword
          ).subscribe({
            next: (result) => {
              this.isProcessingRegistration = false;
              this.verificationLoading = false;

              let message = result.message;
              if (result.cartTransferred && result.transferredItemsCount > 0) {
                message += ` Перенесено товаров: ${result.transferredItemsCount}.`;
              }

              this.modalService.success('Добро пожаловать!', message).then(() => {
                // Reload the page to show authenticated cart
                window.location.reload();
              });
            },
            error: (error) => {
              this.isProcessingRegistration = false;
              this.verificationLoading = false;
              console.error('Login and cart transfer error:', error);
              this.modalService.error('Ошибка', 'Не удалось войти в систему. Попробуйте войти вручную.');
            }
          });
        },
        error: (error) => {
          this.isProcessingRegistration = false;
          this.verificationLoading = false;
          this.handleVerificationError(error);
        }
      });
    }
  }

  private handleRegistrationError(error: any): void {
    if (error.error) {
      // Handle field-specific errors
      this.fieldErrors = error.error;

      // Handle non-field errors
      if (error.error.non_field_errors) {
        this.registrationError = error.error.non_field_errors.join(', ');
      }
    } else {
      this.registrationError = error.message || 'Произошла ошибка при регистрации';
    }
  }

  private handleVerificationError(error: any): void {
    if (error.error && error.error.non_field_errors) {
      this.registrationError = error.error.non_field_errors.join(', ');
    } else {
      this.registrationError = error.message || 'Произошла ошибка при верификации';
    }
  }

  private clearFieldErrors(): void {
    this.fieldErrors = {};
  }

  hasFieldError(fieldName: string): boolean {
    return this.fieldErrors[fieldName] && this.fieldErrors[fieldName].length > 0;
  }

  getFieldError(fieldName: string): string {
    return this.hasFieldError(fieldName) ? this.fieldErrors[fieldName][0] : '';
  }
}

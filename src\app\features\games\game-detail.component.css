/* Game Detail Specific Styles */

/* Image hover effects */
.game-cover img {
  transition: transform 0.3s ease;
}

.game-cover:hover img {
  transform: scale(1.05);
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 15px 35px rgba(147, 51, 234, 0.4);
}

/* Back button hover */
.back-button {
  transition: all 0.2s ease;
}

.back-button:hover {
  transform: translateX(-4px);
}

/* Section cards */
.detail-section {
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.detail-section:hover {
  border-color: rgba(148, 163, 184, 0.3);
  transform: translateY(-2px);
}

/* Gallery image hover effects */
.gallery-image {
  transition: transform 0.3s ease;
  cursor: pointer;
}

.gallery-image:hover {
  transform: scale(1.05);
}

/* Badge animations */
.badge {
  transition: all 0.2s ease;
}

.badge:hover {
  transform: scale(1.05);
}

/* Price highlight */
.price-text {
  text-shadow: 0 0 15px rgba(34, 197, 94, 0.4);
}

/* Loading animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade in animation for content */
.game-content {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .game-header {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .game-title {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .game-subtitle {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .detail-sections {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 640px) {
  .game-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-button {
    width: 100%;
    text-align: center;
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }

  .price-badges {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

/* Text content styling */
.description-text {
  line-height: 1.6;
  font-size: 0.875rem;
}

.section-title {
  position: relative;
  padding-bottom: 0.5rem;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 2px;
  background: linear-gradient(90deg, #a855f7, #ec4899);
  border-radius: 1px;
}

/* Error state styling */
.error-state {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Disabled button styling */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar for long content */
.detail-section::-webkit-scrollbar {
  width: 8px;
}

.detail-section::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
}

.detail-section::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

.detail-section::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Image loading placeholder */
.image-placeholder {
  background: linear-gradient(45deg, #374151, #4b5563);
  background-size: 400% 400%;
  animation: gradientShift 2s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Beautiful Gallery Styles */
.gallery-grid {
  display: grid;
  gap: 1rem;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.gallery-item img {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.3) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

/* Gallery Modal Styles */
.gallery-modal {
  backdrop-filter: blur(8px);
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
  }
}

.modal-image {
  max-width: 90vw;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
  animation: imageSlideIn 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes imageSlideIn {
  from {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.modal-button {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.modal-button:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.2);
}

.thumbnail-nav {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 9999px;
  padding: 0.75rem;
}

.thumbnail-item {
  transition: all 0.3s ease;
  border-radius: 0.5rem;
  overflow: hidden;
}

.thumbnail-item:hover {
  transform: scale(1.1);
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.thumbnail-item.active {
  ring: 2px;
  ring-color: #60a5fa;
  transform: scale(1.05);
}

/* Responsive Gallery */
@media (max-width: 1280px) {
  .gallery-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .modal-image {
    max-width: 95vw;
    max-height: 70vh;
  }

  .thumbnail-nav {
    max-width: 90vw;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .thumbnail-nav::-webkit-scrollbar {
    display: none;
  }
}

/* Loading states for gallery images */
.gallery-item.loading {
  background: linear-gradient(45deg, #374151, #4b5563);
  background-size: 400% 400%;
  animation: gradientShift 2s ease infinite;
}

/* Zoom effect for gallery items */
.gallery-zoom-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
  border-radius: 50%;
  padding: 0.75rem;
}

.gallery-item:hover .gallery-zoom-icon {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
}

/* Image counter badge */
.image-counter {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .image-counter {
  opacity: 1;
}

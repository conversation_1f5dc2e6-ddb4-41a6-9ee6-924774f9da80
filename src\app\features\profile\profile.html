<!-- Profile Page Container -->
<div
  class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-950 to-gray-900 pt-20"
>
  <div class="flex flex-col lg:flex-row min-h-[calc(100vh-5rem)] profile-layout">
    <!-- Main Content Area -->
    <div class="flex-1 profile-content">
      <div class="px-4 lg:px-8 py-6">
        <div class="max-w-6xl mx-auto">
          <!-- Tab Navigation -->
          <div class="mb-6">
            <!-- Desktop: Horizontal tabs -->
            <div class="hidden sm:flex space-x-1">
              <button
                *ngFor="let tab of tabs"
                (click)="setActiveTab(tab.id)"
                [class]="'flex-1 px-6 py-4 text-sm font-medium transition-all duration-300 whitespace-nowrap text-center relative ' +
                         (activeTab === tab.id ? 'bg-slate-800/50 text-white rounded-t-lg border-b-2 border-blue-400 z-10' :
                          'bg-slate-700/20 text-gray-300 hover:text-white hover:bg-slate-700/40 rounded-t-lg border-b-2 border-transparent')"
              >
                {{ tab.label }}
              </button>
            </div>

            <!-- Mobile: Vertical tabs -->
            <div class="sm:hidden space-y-2">
              <button
                *ngFor="let tab of tabs"
                (click)="setActiveTab(tab.id)"
                [class]="'w-full px-4 py-3 text-xs font-medium transition-all duration-300 text-left relative ' +
                         (activeTab === tab.id ? 'bg-slate-800/50 text-white rounded-lg border-l-4 border-blue-400' :
                          'bg-slate-700/20 text-gray-300 hover:text-white hover:bg-slate-700/40 rounded-lg border-l-4 border-transparent')"
              >
                {{ tab.label }}
              </button>
            </div>

            <!-- Tab content background connector (desktop only) -->
            <div class="hidden sm:block bg-slate-800/50 h-1 -mt-0.5 rounded-t-sm"></div>
          </div>

          <!-- Content Area -->
          <div class="bg-slate-800/50 rounded-lg sm:rounded-b-lg sm:rounded-tr-lg p-4 sm:p-6 lg:p-8">
        <!-- Loading State -->
        <app-loading-spinner *ngIf="isLoading" [overlay]="true">
        </app-loading-spinner>

        <!-- Error State -->
        <div
          *ngIf="errorMessage && !isLoading"
          class="flex items-center justify-center h-64"
        >
          <div class="text-center">
            <div
              class="bg-red-500/20 border border-red-500/50 rounded-lg p-6 max-w-md"
            >
              <h3 class="text-red-300 font-semibold mb-2">Ошибка загрузки</h3>
              <p class="text-red-200 mb-4">{{ errorMessage }}</p>
              <button
                (click)="loadUserProfile()"
                class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Попробовать снова
              </button>
            </div>
          </div>
        </div>

        <!-- Profile Content -->
        <div *ngIf="userProfile && !isLoading">
          <!-- Child Route Content (e.g., game details) -->
          <div *ngIf="isChildRoute">
            <router-outlet></router-outlet>
          </div>

          <!-- Tab Content (when not on child route) -->
          <div *ngIf="!isChildRoute">
            <!-- Settings Tab Content -->
            <app-profile-settings *ngIf="activeTab === 'settings'"></app-profile-settings>

            <!-- Library Tab Content -->
            <app-user-library *ngIf="activeTab === 'library'"></app-user-library>

            <!-- Purchases Tab Content -->
            <app-purchase-history *ngIf="activeTab === 'purchases'"></app-purchase-history>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</div>

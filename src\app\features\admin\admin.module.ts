import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';

import { Admin } from './admin';
import { SharedModule } from '../../shared/shared.module';
import { ProfileModule } from '../profile/profile.module';

@NgModule({
  declarations: [
    Admin
  ],
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
    ProfileModule // Import ProfileModule to use exported admin components
  ],
  exports: [
    Admin
  ]
})
export class AdminModule { }

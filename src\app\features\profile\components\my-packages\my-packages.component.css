/* Profile section styling to match settings page */
.profile-section {
  transition: all 0.3s ease;
}

.profile-section:hover {
  background-color: rgba(15, 23, 42, 0.8);
  border-color: rgba(71, 85, 105, 0.6);
}

/* Custom scrollbar for modal */
.max-h-\[80vh\]::-webkit-scrollbar {
  width: 6px;
}

.max-h-\[80vh\]::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 3px;
}

.max-h-\[80vh\]::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 3px;
}

.max-h-\[80vh\]::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* Modal backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  backdrop-filter: blur(12px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-w-2xl {
    max-width: 95vw;
  }
}

/* Focus states for accessibility */
button:focus-visible,
input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth transitions for interactive elements */
button,
input,
.profile-section {
  transition: all 0.2s ease-in-out;
}

/* Loading spinner animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Game card styles */
.game-card {
  transition: all 0.3s ease;
}

.game-card:hover {
  transform: translateY(-2px);
}

.game-card:hover .aspect-square {
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.game-card.selected .aspect-square {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.game-card .aspect-square {
  transition: all 0.3s ease;
  border-radius: 8px;
}

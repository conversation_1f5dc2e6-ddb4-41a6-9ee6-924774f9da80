import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { AuthService } from '../../../../core/services/auth.service';
import { LoadingService } from '../../../../core/services/loading.service';
import { ModalService } from '../../../../core/services/modal.service';

@Component({
  selector: 'app-registration',
  standalone: false,
  templateUrl: './registration.html',
  styleUrl: './registration.css'
})
export class Registration implements OnInit, OnDestroy {
  registrationForm: FormGroup;
  verificationForm: FormGroup;
  showCodeField = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  registeredEmail = '';
  fieldErrors: { [key: string]: string } = {};

  private registrationSubscription?: Subscription;
  private verificationSubscription?: Subscription;
  private resendSubscription?: Subscription;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private loadingService: LoadingService,
    private modalService: ModalService
  ) {
    this.registrationForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });

    this.verificationForm = this.fb.group({
      code: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]]
    });
  }

  ngOnInit(): void {
    // Subscribe to different loading states
    this.registrationSubscription = this.loadingService.getLoading('registration').subscribe(
      loading => {
        if (!this.showCodeField) {
          this.isLoading = loading;
        }
      }
    );

    this.verificationSubscription = this.loadingService.getLoading('verification').subscribe(
      loading => {
        if (this.showCodeField) {
          this.isLoading = loading;
        }
      }
    );

    this.resendSubscription = this.loadingService.getLoading('resend-code').subscribe(
      loading => {
        if (this.showCodeField) {
          this.isLoading = loading;
        }
      }
    );


  }

  ngOnDestroy(): void {
    this.registrationSubscription?.unsubscribe();
    this.verificationSubscription?.unsubscribe();
    this.resendSubscription?.unsubscribe();
  }

  onRegisterClick() {
    if (this.registrationForm.valid) {
      this.errorMessage = '';
      this.clearFieldErrors();

      const credentials = this.registrationForm.value;

      this.authService.register(credentials).subscribe({
        next: (response) => {
          this.registeredEmail = response.email;
          this.showCodeField = true;
          this.successMessage = ''; // Clear success message to avoid duplication
          this.modalService.success('Регистрация', 'Код подтверждения отправлен на ваш email');
        },
        error: (error) => {
          this.handleRegistrationError(error);
        }
      });
    } else {
      this.showFormValidationErrors(this.registrationForm);
    }
  }

  onVerifyCode() {
    if (this.verificationForm.valid) {
      this.errorMessage = '';

      const verificationData = {
        email: this.registeredEmail,
        code: this.verificationForm.value.code
      };

      this.authService.verifyCode(verificationData).subscribe({
        next: (response) => {
          this.successMessage = response.detail;
          // Show success modal and redirect to login
          this.modalService.success('Успешная верификация', 'Ваш аккаунт активирован! Теперь вы можете войти в систему.').then(() => {
            this.router.navigate(['/login']);
          });
        },
        error: (error) => {
          this.handleVerificationError(error);
        }
      });
    } else {
      this.showFormValidationErrors(this.verificationForm);
    }
  }

  resendCode() {
    // Use the dedicated resend-code endpoint
    const resendData = {
      email: this.registeredEmail
    };

    this.errorMessage = '';

    this.authService.resendCode(resendData).subscribe({
      next: (response) => {
        this.successMessage = response.detail;
        this.verificationForm.reset();
      },
      error: (error) => {
        this.handleResendCodeError(error);
      }
    });
  }

  getFieldError(fieldName: string, formGroup: FormGroup = this.registrationForm): string {
    // First check for API field errors
    if (this.fieldErrors[fieldName]) {
      return this.fieldErrors[fieldName];
    }

    // Then check for form validation errors
    const field = formGroup.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return 'Это поле обязательно';
      }
      if (field.errors['email']) {
        return 'Введите корректный email';
      }
      if (field.errors['minlength']) {
        return `Минимум ${field.errors['minlength'].requiredLength} символов`;
      }
      if (field.errors['pattern']) {
        return 'Код должен содержать 6 цифр';
      }
    }
    return '';
  }

  isFieldInvalid(fieldName: string, formGroup: FormGroup = this.registrationForm): boolean {
    // Check for API field errors first
    if (this.fieldErrors[fieldName]) {
      return true;
    }

    // Then check for form validation errors
    const field = formGroup.get(fieldName);
    return !!(field && field.errors && field.touched);
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private handleRegistrationError(error: any) {
    this.clearFieldErrors();
    const errors: string[] = [];

    if (error.error) {
      // Handle field-specific errors (e.g., email already exists)
      if (error.error.email && error.error.email.length > 0) {
        this.fieldErrors['email'] = error.error.email[0];
        errors.push(`Email: ${error.error.email[0]}`);
      }
      if (error.error.password && error.error.password.length > 0) {
        this.fieldErrors['password'] = error.error.password[0];
        errors.push(`Пароль: ${error.error.password[0]}`);
      }

      // Handle general errors
      if (error.error.non_field_errors && error.error.non_field_errors.length > 0) {
        errors.push(error.error.non_field_errors[0]);
      }

      // Show errors in modal
      if (errors.length > 0) {
        this.modalService.error('Ошибка регистрации', errors.join('\n'));
      } else {
        this.modalService.error('Ошибка регистрации', 'Произошла ошибка при регистрации');
      }
    } else {
      this.modalService.error('Ошибка регистрации', error.message || 'Произошла ошибка при регистрации');
    }
  }

  private handleVerificationError(error: any) {
    if (error.error && error.error.non_field_errors && error.error.non_field_errors.length > 0) {
      // Handle specific verification errors like "Incorrect code" or "User not found"
      this.modalService.error('Ошибка верификации', error.error.non_field_errors[0]);
    } else {
      this.modalService.error('Ошибка верификации', error.message || 'Ошибка при проверке кода');
    }
  }

  private handleResendCodeError(error: any) {
    this.clearFieldErrors();

    if (error.error) {
      // Handle field-specific errors (e.g., email not found, already verified)
      if (error.error.email && error.error.email.length > 0) {
        this.modalService.error('Ошибка отправки кода', error.error.email[0]);
      } else if (error.error.non_field_errors && error.error.non_field_errors.length > 0) {
        this.modalService.error('Ошибка отправки кода', error.error.non_field_errors[0]);
      } else {
        this.modalService.error('Ошибка отправки кода', 'Ошибка при повторной отправке кода');
      }
    } else {
      this.modalService.error('Ошибка отправки кода', error.message || 'Ошибка при повторной отправке кода');
    }
  }

  private showFormValidationErrors(formGroup: FormGroup): void {
    this.markFormGroupTouched(formGroup);
    const errors: string[] = [];

    Object.keys(formGroup.controls).forEach(key => {
      const fieldError = this.getFieldError(key, formGroup);
      if (fieldError) {
        errors.push(fieldError);
      }
    });

    if (errors.length > 0) {
      this.modalService.error('Ошибки в форме', errors.join('\n'));
    }
  }

  private clearFieldErrors() {
    this.fieldErrors = {};
  }
}

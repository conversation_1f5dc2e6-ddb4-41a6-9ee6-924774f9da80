import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import { 
  LibraryItem, 
  LibraryResponse, 
  AddToLibraryRequest, 
  AddToLibraryResponse, 
  LibraryFilters 
} from '../models/library.model';

@Injectable({
  providedIn: 'root'
})
export class LibraryService {
  private apiUrl = `${environment.apiUrl}/api/library`;

  constructor(private http: HttpClient) {}

  /**
   * Get user's library with optional filtering and pagination
   * @param filters - Optional filters for search and ordering
   * @param page - Page number for pagination
   * @param pageSize - Number of items per page
   */
  getLibrary(filters?: LibraryFilters, page?: number, pageSize?: number): Observable<LibraryResponse> {
    let params = new HttpParams();

    if (filters?.search) {
      params = params.set('search', filters.search);
    }

    if (filters?.ordering) {
      params = params.set('ordering', filters.ordering);
    }

    if (page) {
      params = params.set('page', page.toString());
    }

    if (pageSize) {
      params = params.set('page_size', pageSize.toString());
    }

    return this.http.get<LibraryResponse>(`${this.apiUrl}/`, { params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Manually add a game to user's library (admin only)
   * @param request - Request containing user_id and game_id
   */
  addToLibrary(request: AddToLibraryRequest): Observable<AddToLibraryResponse> {
    return this.http.post<AddToLibraryResponse>(`${this.apiUrl}/add/`, request).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Check if a game is in user's library
   * @param gameId - Game ID to check
   */
  isGameInLibrary(gameId: number): Observable<boolean> {
    return new Observable(observer => {
      this.getLibrary().subscribe({
        next: (response) => {
          const isInLibrary = response.results.some(item => item.game.id === gameId);
          observer.next(isInLibrary);
          observer.complete();
        },
        error: (error) => {
          observer.error(error);
        }
      });
    });
  }

  /**
   * Handle HTTP errors
   * @param error - HTTP error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Произошла неизвестная ошибка';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Ошибка: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          if (error.error?.non_field_errors) {
            errorMessage = error.error.non_field_errors[0];
          } else if (error.error?.user_id) {
            errorMessage = error.error.user_id[0];
          } else if (error.error?.game_id) {
            errorMessage = error.error.game_id[0];
          } else if (error.error?.game) {
            errorMessage = error.error.game[0];
          } else {
            errorMessage = 'Неверные данные запроса';
          }
          break;
        case 401:
          errorMessage = 'Необходима авторизация';
          break;
        case 403:
          errorMessage = 'Недостаточно прав доступа';
          break;
        case 404:
          errorMessage = 'Ресурс не найден';
          break;
        case 500:
          errorMessage = 'Внутренняя ошибка сервера';
          break;
        default:
          errorMessage = `Ошибка сервера: ${error.status}`;
      }
    }

    console.error('Library Service Error:', error);
    return throwError(() => new Error(errorMessage));
  }
}

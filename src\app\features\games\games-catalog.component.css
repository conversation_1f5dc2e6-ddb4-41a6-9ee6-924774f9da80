/* Games Catalog Specific Styles */

/* Card hover effects */
.game-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.game-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Image hover effects */
.game-image {
  transition: transform 0.3s ease;
}

.game-card:hover .game-image {
  transform: scale(1.05);
}

/* Button hover effects */
.btn-primary {
  transition: all 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Search input focus effects */
.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Badge animations */
.badge {
  transition: all 0.2s ease;
}

.badge:hover {
  transform: scale(1.05);
}

/* Grid responsive adjustments */
@media (max-width: 768px) {
  .games-grid {
    gap: 1rem;
  }
  
  .game-card {
    margin-bottom: 1rem;
  }
  
  .search-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .pagination {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Text truncation utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Price highlight */
.price-text {
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

/* Empty state styling */
.empty-state {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pagination styling */
.pagination-button {
  transition: all 0.2s ease;
}

.pagination-button:hover:not(:disabled) {
  transform: translateY(-1px);
}

/* Filter controls styling */
.filter-controls {
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.filter-controls:hover {
  border-color: rgba(148, 163, 184, 0.3);
}

/* Sort dropdown styling */
.sort-select {
  transition: all 0.2s ease;
}

.sort-select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Card content spacing */
.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-actions {
  margin-top: auto;
  padding-top: 1rem;
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .page-title {
    font-size: 2rem;
    line-height: 2.5rem;
  }
  
  .page-subtitle {
    font-size: 1rem;
    line-height: 1.5rem;
  }
  
  .card-title {
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

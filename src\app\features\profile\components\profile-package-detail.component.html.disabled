<div class="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 p-4 lg:p-6">
  <div class="max-w-4xl mx-auto">
    <!-- Back Button -->
    <div class="mb-6">
      <button
        (click)="goBack()"
        class="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
      >
        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        Назад к пакетам
      </button>
    </div>

    <!-- Loading State -->
    <div *ngIf="packageLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error State -->
    <div *ngIf="packageError && !packageLoading" class="bg-red-500/20 border border-red-500/50 rounded-lg p-4 mb-6">
      <p class="text-red-400">{{ packageError }}</p>
    </div>

    <!-- Package Details -->
    <div *ngIf="package && !packageLoading" class="bg-slate-800/50 backdrop-blur-sm rounded-lg p-6 lg:p-8">
      <!-- Header -->
      <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6 mb-8">
        <div class="flex-1">
          <h1 class="text-3xl lg:text-4xl font-bold text-white mb-4">{{ package.name }}</h1>
          <p class="text-gray-300 text-lg leading-relaxed">{{ package.description }}</p>
        </div>
        
        <div class="lg:text-right">
          <div class="text-4xl lg:text-5xl font-bold text-blue-400 mb-2">{{ formatPrice(package.price) }}</div>
          <div class="text-gray-400">{{ getPricePerDay() }} ₸ за день</div>
          <div class="text-gray-400 text-sm">{{ package.duration_days }} дней доступа</div>
        </div>
      </div>

      <!-- Package Info Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Benefits -->
        <div>
          <h3 class="text-xl font-semibold text-white mb-4">Преимущества</h3>
          <div class="space-y-3">
            <div *ngIf="package.benefit_1" class="flex items-start gap-3">
              <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-300">{{ package.benefit_1 }}</span>
            </div>
            <div *ngIf="package.benefit_2" class="flex items-start gap-3">
              <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-300">{{ package.benefit_2 }}</span>
            </div>
            <div *ngIf="package.benefit_3" class="flex items-start gap-3">
              <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-300">{{ package.benefit_3 }}</span>
            </div>
            <div class="flex items-start gap-3">
              <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-300">{{ package.duration_days }} дней полного доступа</span>
            </div>
            <div class="flex items-start gap-3">
              <svg class="h-5 w-5 text-green-400 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              <span class="text-gray-300">Выбор до {{ package.max_selectable_games }} игр</span>
            </div>
          </div>
        </div>

        <!-- Package Details -->
        <div>
          <h3 class="text-xl font-semibold text-white mb-4">Детали пакета</h3>
          <div class="space-y-4">
            <div class="bg-slate-700/30 rounded-lg p-4">
              <div class="text-gray-400 text-sm mb-1">Продолжительность</div>
              <div class="text-white font-semibold">{{ package.duration_days }} дней</div>
            </div>
            <div class="bg-slate-700/30 rounded-lg p-4">
              <div class="text-gray-400 text-sm mb-1">Максимум игр</div>
              <div class="text-white font-semibold">{{ package.max_selectable_games }} игр</div>
            </div>
            <div class="bg-slate-700/30 rounded-lg p-4">
              <div class="text-gray-400 text-sm mb-1">Игр в пакете</div>
              <div class="text-white font-semibold">{{ package.games.length }} игр</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Included Games -->
      <div class="mb-8">
        <h3 class="text-xl font-semibold text-white mb-4">Игры в пакете</h3>
        <div *ngIf="package.games.length > 0" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
          <div *ngFor="let game of package.games" 
               class="bg-slate-700/30 rounded-lg p-3 flex items-center justify-between">
            <span class="text-white text-sm">{{ game.title }}</span>
            <span class="text-xs text-gray-400">ID: {{ game.id }}</span>
          </div>
        </div>
        <div *ngIf="package.games.length === 0" class="text-gray-400 text-center py-8">
          В этом пакете пока нет игр
        </div>
      </div>

      <!-- Quantity Selection and Add to Cart -->
      <div class="border-t border-slate-700 pt-6">
        <div class="flex flex-col sm:flex-row gap-4 items-center justify-between">
          <div class="flex items-center gap-4">
            <label class="text-gray-300 text-sm font-medium">Количество:</label>
            <select 
              #quantitySelect
              class="bg-slate-700/50 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
            </select>
          </div>

          <div class="flex gap-3">
            <button
              (click)="addToCart(+quantitySelect.value)"
              [disabled]="addingToCart"
              class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <div *ngIf="addingToCart" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <svg *ngIf="!addingToCart" class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
              </svg>
              {{ addingToCart ? 'Добавление...' : 'Добавить в корзину' }}
            </button>

            <button
              (click)="goToCart()"
              class="bg-slate-600 hover:bg-slate-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
            >
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15"></path>
              </svg>
              Корзина
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

import { Component, On<PERSON>nit, On<PERSON><PERSON>roy } from '@angular/core';
import { AuthService, UserProfile } from '../../core/services/auth.service';
import { GameService } from '../../core/services/game.service';
import { UserService } from '../../core/services/user.service';
import { UserSummaryService, UserSummary } from '../../core/services/user-summary.service';
import { ModalService } from '../../core/services/modal.service';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { Game } from '../../core/models/game.model';
import { User, UserFilters } from '../../core/models/user.model';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-profile',
  standalone: false,
  templateUrl: './profile.html',
  styleUrl: './profile.css'
})
export class Profile implements OnInit, OnD<PERSON>roy {
  userProfile: UserProfile | null = null;
  isLoading = true;
  errorMessage = '';

  // User summary data
  userSummary: UserSummary | null = null;
  private summarySubscription?: Subscription;
  private routerSubscription?: Subscription;



  // Tab management
  activeTab: string = 'settings';
  isChildRoute: boolean = false;

  // Tab definitions
  tabs = [
    {
      id: 'settings',
      label: 'ПАРАМЕТРЫ ПРОФИЛЯ'
    },
    {
      id: 'library',
      label: 'МОИ ИГРЫ'
    },
    {
      id: 'purchases',
      label: 'ИСТОРИЯ ПОКУПОК'
    }
  ];

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private userSummaryService: UserSummaryService,
    private modalService: ModalService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.loadUserProfile();
    this.loadUserSummary();
    this.setupRouterSubscription();
    this.updateActiveTabFromRoute();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.summarySubscription) {
      this.summarySubscription.unsubscribe();
    }
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }

  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.errorMessage = '';

    this.authService.getUserProfile().subscribe({
      next: (profile) => {
        this.userProfile = profile;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = error.message || 'Failed to load profile';
        this.isLoading = false;
      }
    });
  }

  loadUserSummary(): void {
    // Subscribe to the summary observable for real-time updates
    this.summarySubscription = this.userSummaryService.summary$.subscribe(summary => {
      this.userSummary = summary;
    });

    // Load initial summary data
    this.userSummaryService.getUserSummary().subscribe({
      next: (summary) => {
        // Summary is automatically updated via the subscription above
      },
      error: (error) => {
        console.error('Failed to load user summary:', error);
        // Don't show error to user as this is supplementary data
      }
    });
  }

  onLogout(): void {
    this.authService.logout();
  }

  refreshProfile(): void {
    this.loadUserProfile();
    this.refreshUserSummary();
  }

  refreshUserSummary(): void {
    this.userSummaryService.refreshSummary();
  }

  // Setup router subscription to update active tab
  setupRouterSubscription(): void {
    this.routerSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe(() => {
        this.updateActiveTabFromRoute();
      });
  }

  // Update active tab based on current route
  updateActiveTabFromRoute(): void {
    const url = this.router.url;

    // Check if we're on a child route
    if (url.includes('/profile/library/games/')) {
      this.isChildRoute = true;
      this.activeTab = 'library';
    } else {
      this.isChildRoute = false;

      // Determine active tab from route
      if (url.includes('/profile/settings')) {
        this.activeTab = 'settings';
      } else if (url.includes('/profile/library')) {
        this.activeTab = 'library';
      } else if (url.includes('/profile/purchases')) {
        this.activeTab = 'purchases';
      } else if (url.includes('/profile/my-packages')) {
        this.activeTab = 'my-packages';
      } else {
        this.activeTab = 'settings'; // Default
      }
    }
  }



  verifyToken(): void {
    this.authService.verifyToken().subscribe({
      next: (response) => {
        console.log('Token verified:', response);
        this.modalService.success('Успех', 'Токен действителен!');
      },
      error: (error) => {
        console.error('Token verification failed:', error);
        this.modalService.error('Ошибка', 'Проверка токена не удалась: ' + error.message);
      }
    });
  }
  // Tab management methods
  setActiveTab(tabId: string): void {
    this.activeTab = tabId;
    this.isChildRoute = false;

    // Navigate to the appropriate route
    this.router.navigate(['/profile', tabId]);
  }







}

/* Modal animations */
.fixed {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal content animation */
.inline-block {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced backdrop blur */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

/* Button hover effects */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

button:active {
  transform: translateY(0);
}

/* Icon animations */
svg {
  transition: all 0.3s ease;
}

/* Modal border glow effect */
.border-slate-700 {
  border-color: rgba(51, 65, 85, 0.8);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* Text shadow for better readability */
.text-white {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.text-gray-300 {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

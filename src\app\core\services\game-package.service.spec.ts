import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { GamePackageService } from './game-package.service';
import {
  GamePackage,
  CreateGamePackageRequest,
  GamePackageFilters,
  GamePackageListResponse,
  PackagePurchaseResponse,
  MyPackagesResponse,
  SelectGamesResponse,
  PackageSubscription
} from '../models/game-package.model';
import { environment } from '../../environments/environment';

describe('GamePackageService', () => {
  let service: GamePackageService;
  let httpMock: HttpTestingController;
  const apiUrl = `${environment.apiUrl}/api/game-packages`;
  const packagesApiUrl = `${environment.apiUrl}/api/packages`;
  const myPackagesUrl = `${environment.apiUrl}/api/my-packages`;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [GamePackageService]
    });
    service = TestBed.inject(GamePackageService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getGamePackages', () => {
    it('should fetch packages without filters', () => {
      const mockResponse: GamePackageListResponse = {
        count: 1,
        next: null,
        previous: null,
        results: [
          {
            id: 1,
            name: 'Premium Package',
            description: '4 games for 1 month',
            benefit_1: 'Benefit 1',
            benefit_2: 'Benefit 2',
            benefit_3: 'Benefit 3',
            price: '1990.00',
            duration_days: 30,
            games: [],
            max_selectable_games: 2
          }
        ]
      };

      service.getGamePackages().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('GET');
      expect(req.request.params.keys().length).toBe(0);
      req.flush(mockResponse);
    });

    it('should fetch packages with filters', () => {
      const filters: GamePackageFilters = {
        search: 'premium',
        ordering: '-id'
      };

      service.getGamePackages(filters, 1, 10).subscribe();

      const req = httpMock.expectOne(request => 
        request.url === `${apiUrl}/` && 
        request.params.get('search') === 'premium' &&
        request.params.get('ordering') === '-id' &&
        request.params.get('page') === '1' &&
        request.params.get('page_size') === '10'
      );
      expect(req.request.method).toBe('GET');
      req.flush({ count: 0, next: null, previous: null, results: [] });
    });
  });

  describe('getGamePackage', () => {
    it('should fetch a single package', () => {
      const mockPackage: GamePackage = {
        id: 1,
        name: 'Premium Package',
        description: '4 games for 1 month',
        benefit_1: 'Benefit 1',
        benefit_2: 'Benefit 2',
        benefit_3: 'Benefit 3',
        price: '1990.00',
        duration_days: 30,
        games: [],
        max_selectable_games: 2
      };

      service.getGamePackage(1).subscribe(packageData => {
        expect(packageData).toEqual(mockPackage);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('GET');
      req.flush(mockPackage);
    });
  });

  describe('createGamePackage', () => {
    it('should create a package', () => {
      const packageData: CreateGamePackageRequest = {
        name: 'New Package',
        description: 'New Description',
        price: '2990.00',
        duration_days: 30,
        game_ids: [1, 2],
        max_selectable_games: 2
      };

      const mockResponse: GamePackage = {
        id: 2,
        name: packageData.name,
        description: packageData.description,
        benefit_1: '',
        benefit_2: '',
        benefit_3: '',
        price: packageData.price,
        duration_days: packageData.duration_days,
        games: [],
        max_selectable_games: packageData.max_selectable_games
      };

      service.createGamePackage(packageData).subscribe(packageResponse => {
        expect(packageResponse).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual(packageData);
      req.flush(mockResponse);
    });
  });

  describe('updateGamePackage', () => {
    it('should update a package', () => {
      const packageData = { name: 'Updated Package' };
      const mockResponse: GamePackage = {
        id: 1,
        name: 'Updated Package',
        description: 'Test Description',
        benefit_1: '',
        benefit_2: '',
        benefit_3: '',
        price: '1990.00',
        duration_days: 30,
        games: [],
        max_selectable_games: 2
      };

      service.updateGamePackage(1, packageData).subscribe(packageResponse => {
        expect(packageResponse).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('PATCH');
      req.flush(mockResponse);
    });
  });

  describe('deleteGamePackage', () => {
    it('should delete a package', () => {
      service.deleteGamePackage(1).subscribe();

      const req = httpMock.expectOne(`${apiUrl}/1/`);
      expect(req.request.method).toBe('DELETE');
      req.flush(null);
    });
  });

  describe('purchasePackage', () => {
    it('should purchase a package', () => {
      const mockResponse: PackagePurchaseResponse = {
        detail: 'Пакет успешно приобретён.',
        subscription_id: 1,
        expires_at: '2025-08-20T00:00:00Z',
        max_selectable_games: 2
      };

      service.purchasePackage(1).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${packagesApiUrl}/purchase/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ package_id: 1 });
      req.flush(mockResponse);
    });
  });

  describe('getMyPackages', () => {
    it('should fetch user packages', () => {
      const mockResponse: MyPackagesResponse = {
        count: 1,
        next: null,
        previous: null,
        results: [{
          id: 1,
          package_name: 'Premium Package',
          selected_games: [{ id: 1, title: 'Game 1' }],
          available_games: [{ id: 2, title: 'Game 2' }],
          remaining_slots: 1,
          expires_at: '2025-08-20T00:00:00Z'
        }]
      };

      service.getMyPackages().subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${myPackagesUrl}/?page=1&page_size=12`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });
  });

  describe('selectGamesFromPackage', () => {
    it('should select games from package', () => {
      const mockResponse: SelectGamesResponse = {
        detail: 'Выбрано игр: 2',
        remaining_slots: 0,
        selected_games: [
          { id: 2, title: 'Game 2' },
          { id: 3, title: 'Game 3' }
        ]
      };

      service.selectGamesFromPackage(1, [2, 3]).subscribe(response => {
        expect(response).toEqual(mockResponse);
      });

      const req = httpMock.expectOne(`${packagesApiUrl}/select-games/`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({
        subscription_id: 1,
        game_ids: [2, 3]
      });
      req.flush(mockResponse);
    });
  });

  describe('error handling', () => {
    it('should handle HTTP errors', () => {
      const errorResponse = { status: 400, statusText: 'Bad Request' };
      const errorData = { non_field_errors: ['Invalid data'] };

      service.getGamePackages().subscribe({
        next: () => fail('should have failed with 400 error'),
        error: (error) => {
          expect(error.message).toBe('Invalid data');
        }
      });

      const req = httpMock.expectOne(`${apiUrl}/`);
      req.flush(errorData, errorResponse);
    });
  });
});

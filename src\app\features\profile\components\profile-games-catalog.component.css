/* Profile Games Catalog Styles */

/* Line clamp utilities for text truncation */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Custom scrollbar for select elements */
select::-webkit-scrollbar {
  width: 8px;
}

select::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.5);
  border-radius: 4px;
}

select::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.6);
  border-radius: 4px;
}

select::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.8);
}

/* Hover effects for game cards */
.group:hover .group-hover\:scale-105 {
  transform: scale(1.05);
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
    gap: 1rem;
  }

  /* Compact layout on mobile */
  .bg-slate-800\/40 {
    padding: 1rem;
  }

  /* Smaller text on mobile */
  h1 {
    font-size: 1.875rem;
  }

  /* Touch-friendly buttons */
  button, select, input {
    min-height: 44px;
  }

  /* Stack price and button vertically on mobile */
  .flex-col {
    align-items: flex-start;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .grid {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1.25rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.5rem;
  }
}

@media (min-width: 1281px) {
  .grid {
    grid-template-columns: repeat(4, minmax(0, 1fr));
    gap: 1.5rem;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .group:hover .group-hover\:scale-105 {
    transform: none;
  }

  .hover\:border-slate-500\/60:hover {
    border-color: inherit;
  }

  .hover\:transform:hover {
    transform: none;
  }

  .hover\:scale-\[1\.02\]:hover {
    transform: none;
  }

  .hover\:bg-blue-700:hover,
  .hover\:bg-orange-700:hover {
    background-color: inherit;
  }
}

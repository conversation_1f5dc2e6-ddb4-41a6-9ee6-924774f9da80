/* Cart Page Specific Styles */

/* Cart item hover effects */
.cart-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cart-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Remove button styling */
.remove-button {
  opacity: 0.7;
  transition: all 0.2s ease;
}

.cart-item:hover .remove-button {
  opacity: 1;
}

.remove-button:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Image hover effects */
.game-image {
  transition: transform 0.3s ease;
  cursor: pointer;
}

.game-image:hover {
  transform: scale(1.05);
}

/* Button hover effects */
.btn-primary {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(147, 51, 234, 0.4);
}

/* Quantity controls */
.quantity-controls {
  border-radius: 0.5rem;
  overflow: hidden;
}

.quantity-btn {
  transition: all 0.2s ease;
  min-width: 2.5rem;
}

.quantity-btn:hover {
  background-color: rgba(51, 65, 85, 0.8);
}

.quantity-display {
  min-width: 3rem;
  text-align: center;
}

/* Price highlight */
.price-text {
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.3);
}

/* Loading animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Empty cart animation */
.empty-cart {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Cart summary styling */
.cart-summary {
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.cart-summary:hover {
  border-color: rgba(148, 163, 184, 0.3);
}

/* Remove button styling */
.remove-btn {
  transition: all 0.2s ease;
}

.remove-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Game title link styling */
.game-title-link {
  transition: color 0.2s ease;
}

.game-title-link:hover {
  color: #60a5fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cart-item {
    padding: 1rem;
  }
  
  .cart-item-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .game-image-container {
    width: 100%;
    height: 8rem;
  }
  
  .cart-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .quantity-price-section {
    flex-direction: column;
    gap: 1rem;
  }
  
  .cart-summary-content {
    flex-direction: column;
    text-align: center;
    gap: 1.5rem;
  }
  
  .summary-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
}

@media (max-width: 640px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  
  .page-title {
    font-size: 2rem;
    line-height: 2.5rem;
  }
  
  .continue-shopping-btn {
    width: 100%;
    text-align: center;
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom scrollbar */
.cart-container::-webkit-scrollbar {
  width: 8px;
}

.cart-container::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 4px;
}

.cart-container::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.5);
  border-radius: 4px;
}

.cart-container::-webkit-scrollbar-thumb:hover {
  background: rgba(148, 163, 184, 0.7);
}

/* Error state styling */
.error-state {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Success state animations */
.success-message {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Disabled state styling */
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Processing overlay */
.processing-overlay {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Processing animation */
@keyframes processingPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.processing-text {
  animation: processingPulse 2s ease-in-out infinite;
}
